package com.tcl.ai.note.voicetotext.audio

import android.content.Context
import android.media.AudioManager
import android.media.AudioManager.OnAudioFocusChangeListener
import android.media.AudioRecordingConfiguration
import android.media.MediaRecorder
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import androidx.annotation.RequiresApi
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.Logger.d
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.voicetotext.states.RecordSpecialState
import com.tcl.ai.note.voicetotext.states.RecordingState
import com.tcl.ai.note.voicetotext.util.AudioToTextConstant
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.IOException

/**
 * 负责音频录制功能的类
 */
object ShowAudioRecorder {
    private const val TAG = "ShowAudioRecorder" // 日志标签
    private const val VIEW_UP_INTERVAL = 500L // 更新文本时间间隔

    private var folderPath: String? = null
    var audioFilePath: String? = null
    private var mediaRecorder: MediaRecorder? = null
    private var startTime: Long = 0
    private var endTime: Long = 0
    private val _recordingState = MutableStateFlow(RecordingState())
    val recordingState = _recordingState.asStateFlow()

    private val _will2HourDialog = MutableStateFlow(false)
    val will2HourDialog = _will2HourDialog.asStateFlow()

    private val _reach2HourDialog = MutableStateFlow(false)
    val reach2HourDialog = _reach2HourDialog.asStateFlow()

    private var runningJob: Job? = null

    var isRecording: Boolean = false
        private set

    /**
     * 开始录音
     */
    fun startRecord(audioPath: String) {
        if (isRecording) return

        try {
            if (mediaRecorder == null) mediaRecorder = MediaRecorder(GlobalContext.instance)
        } catch (exception: RuntimeException) {
            d(TAG, "Unable to init MediaRecorder")
            recordException()
            return
        }

        try {
            mediaRecorder!!.setAudioSource(MediaRecorder.AudioSource.MIC) // 设置音频来源为麦克风
        } catch (exception: RuntimeException) {
            d(TAG, "setAudioSource, Internal Error")
            recordException()
            return
        }

        try {
            mediaRecorder!!.setOutputFormat(MediaRecorder.OutputFormat.AMR_WB) // 设置输出格式
            mediaRecorder!!.setAudioEncoder(MediaRecorder.AudioEncoder.AMR_WB) // 设置音频编码
        } catch (exception: RuntimeException) {
            d(TAG, "Not support format")
            recordException()
            return
        }

        try {
            mediaRecorder!!.setAudioSamplingRate(16000)
        } catch (exception: RuntimeException) {
            d(TAG, "setAudioSamplingRate Error: ${exception.message}")
        }

        audioFilePath = audioPath
        try {
            mediaRecorder!!.setOutputFile(audioFilePath) // 设置音频文件
            // mediaRecorder!!.setMaxDuration(MAX_LENGTH)
            mediaRecorder!!.prepare() // 准备录音
        } catch (exception: IOException) {
            d(TAG, "prepare and setOutputFile, IOException")
            recordException()
            return
        } catch (exception: IllegalStateException) {
            d(TAG, "prepare and setOutputFile, IllegalStateException")
            recordException()
            return
        }

        try {
            mediaRecorder!!.start() // 开始录音
            registerAudioRecordingCallback()
        } catch (e: RuntimeException) {
            Logger.e(TAG, "start failed, maybe quickly start")
            val audioMngr: AudioManager? = GlobalContext.instance.getSystemService(AudioManager::class.java)
            val isInCall = ((audioMngr?.mode == AudioManager.MODE_IN_CALL) ||
                    (audioMngr?.mode == AudioManager.MODE_IN_COMMUNICATION))
            if (isInCall) {
                d(TAG, "Start In Call")
            } else {
                d(TAG, "Start Internal Error ")
            }
            recordException()
            return
        }
        requestAudioFocus() // 请求音频焦点
        isRecording = true
        iconShark = true
        startTime = System.currentTimeMillis()
        endTime = startTime
        mHandler.removeCallbacks(mUpdateMicStatusTimer)
        updateMicStatus()
    }

    fun reset2HourTipState() {
        _will2HourDialog.value = false
        _reach2HourDialog.value = false
    }

    /**
     * 重置录音状态
     */
    fun resetRecordingState() {
        _recordingState.value = RecordingState()
    }

    private fun updateRecordingState(recordingState: RecordingState) {
        _recordingState.value = recordingState
    }

    /**
     * 停止录音
     * @return 返回录音时长
     */
    fun stopRecord(): Long {
        if (!isRecording) return 0L

        try {
            mediaRecorder?.stop() // 停止录音
        } catch (e: RuntimeException) {
            d(TAG, "stop failed, maybe quickly stop")
        }

        // 计算录音时长
        val duration = endTime - startTime

        // 如果录音时长小于最小录制时间，记录日志
        if (duration < AudioToTextConstant.MIN_RECORD_DURATION) {
            d(TAG, "Recording too short: $duration ms, min required: ${AudioToTextConstant.MIN_RECORD_DURATION} ms")
        }

        releaseRecording() // 释放资源
        abandonAudioFocus() // 放弃音频焦点
        return duration // 返回录音时长
    }

    /**
     * 释放录音相关资源
     */
    private fun releaseRecording() {
        val duration = endTime - startTime // 保存当前录音时长
        val currentAudioPath = audioFilePath // 保存当前音频路径
        val currentSpecialState = _recordingState.value.specialState // 保存当前特殊状态

        mediaRecorder?.reset() // 重置录音器
        mediaRecorder?.release() // 释放录音器
        mediaRecorder = null
        isRecording = false
        unregisterAudioRecordingCallback()

        // 更新状态时保留录音时长、音频路径和特殊状态
        _recordingState.value = RecordingState(
            isRecording = false,
            recordingIconVisible = false,
            recordDuration = duration,
            audioPath = currentAudioPath,
            specialState = currentSpecialState // 保留特殊状态，如MaxDurationReached
        )

        mHandler.removeCallbacks(mUpdateMicStatusTimer)
    }

    /**
     * 公共方法：释放录音资源（用于自动保存并开始新录音）
     */
    fun releaseRecordingResources() {
        d(TAG, "releaseRecordingResources: releasing MediaRecorder resources")
        mediaRecorder?.reset() // 重置录音器
        mediaRecorder?.release() // 释放录音器
        mediaRecorder = null
        unregisterAudioRecordingCallback()
        abandonAudioFocus() // 放弃音频焦点
        mHandler.removeCallbacks(mUpdateMicStatusTimer)
    }

    /**
     * 处理录音异常
     */
    private fun recordException() {
        releaseRecording() // 释放资源
        updateRecordingState(RecordingState(isRecording = false,
            specialState = RecordSpecialState.RecordingError(GlobalContext.instance.getString(com.tcl.ai.note.base.R.string.recording_fail))))
    }

    private var iconShark = false

    /**
     * 请求音频焦点
     * @return 请求结果
     */
    private fun requestAudioFocus(): Int {
        val am: AudioManager? = GlobalContext.instance.getSystemService(AudioManager::class.java)
        val result = am?.requestAudioFocus(
            audioFocusListener,
            AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT) ?: -1
        d(TAG, "requestAudioFocus, result = $result")
        return result
    }

    /**
     * 放弃音频焦点
     */
    private fun abandonAudioFocus() {
        val am: AudioManager? = GlobalContext.instance.getSystemService(AudioManager::class.java)
        d(TAG, "abandonAudioFocus, result = ${am?.abandonAudioFocus(audioFocusListener) }")
    }

    // 音频焦点变化监听器
    private val audioFocusListener = OnAudioFocusChangeListener { focusChange ->
        if (focusChange == AudioManager.AUDIOFOCUS_LOSS || focusChange == AudioManager.AUDIOFOCUS_LOSS_TRANSIENT) {
            updateRecordingState(RecordingState(isRecording = false, specialState = RecordSpecialState.AudioFocusLost))
        }
    }

    private val mHandler = Handler(Looper.getMainLooper())
    private val mUpdateMicStatusTimer = Runnable {
        updateMicStatus()
    }

    private fun updateMicStatus() {
        if (!isRecording) return
        endTime = System.currentTimeMillis()
        iconShark = !iconShark
        val duration = endTime - startTime
        d(TAG, "updateMicStatus duration====: $duration")
        val timeDeviation = duration % VIEW_UP_INTERVAL
        updateRecordingState(RecordingState(isRecording = true, recordingIconVisible = iconShark, duration, audioFilePath))

        // 检查是否接近最大录音时长 - 先检查对话框逻辑
        if (duration in (AudioToTextConstant.AUDIO_WILL_REACH_MAX_TRANSCRIPTION_TIME - 200 until AudioToTextConstant.AUDIO_WILL_REACH_MAX_TRANSCRIPTION_TIME + 300)) {
            _will2HourDialog.value = true
        } else if (duration in (AudioToTextConstant.AUDIO_REACH_MAX_TRANSCRIPTION_TIME - 500 until AudioToTextConstant.AUDIO_REACH_MAX_TRANSCRIPTION_TIME)) {
            d(TAG, "updateMicStatus runner, duration: $duration, show reach2HourDialog")
            _reach2HourDialog.value = true
        }

        // 检查是否达到最大录音时长，需要自动停止录音
        if (duration >= AudioToTextConstant.MAX_TRANSCRIPTION_DURATION) {
            // 停止录音定时器
            mHandler.removeCallbacks(mUpdateMicStatusTimer)

            // 先设置特殊状态，然后主动停止录音
            updateRecordingState(RecordingState(
                isRecording = false,
                recordingIconVisible = false,
                recordDuration = duration,
                audioPath = audioFilePath,
                specialState = RecordSpecialState.MaxDurationReached
            ))

            // 主动停止录音硬件，但不调用stopRecord()方法，避免状态被覆盖
            try {
                mediaRecorder?.stop()
                d(TAG, "updateMicStatus: MediaRecorder stopped successfully")
            } catch (e: RuntimeException) {
                d(TAG, "updateMicStatus: MediaRecorder stop failed: $e")
            }

            // 设置isRecording为false，但不释放资源，让autoSaveAndStartNewRecording处理
            isRecording = false

            return
        }

        if (isRecording) {
            if (isScreenOn(GlobalContext.instance)) {
                mHandler.postDelayed(mUpdateMicStatusTimer, VIEW_UP_INTERVAL - timeDeviation)
            }else{
                mHandler.postDelayed(mUpdateMicStatusTimer, 2000)
            }

        } else {
            mHandler.removeCallbacks(mUpdateMicStatusTimer)
        }
    }

    /**
     * 监听系统内正在进行的音频录制活动，此处需要监听麦克风是否被占用
     * */
    private var audioManager: AudioManager? = null
    val callback = object : AudioManager.AudioRecordingCallback() {
        override fun onRecordingConfigChanged(configs: List<AudioRecordingConfiguration>) {
            d(TAG, "onRecordingConfigChanged, configs size: ${configs.size}, isRecording: $isRecording")
            if (isRecording && configs.size > 1) {
                d(TAG, "onRecordingConfigChanged: Multiple recording configs detected, stopping recording")
                ToastUtils.makeWithCancel(R.string.other_apps_recording)
                stopRecord()
            }
        }
    }
    fun registerAudioRecordingCallback() {
        audioManager = GlobalContext.instance.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        audioManager?.registerAudioRecordingCallback(callback, Handler(Looper.getMainLooper()))
    }

    private fun unregisterAudioRecordingCallback() {
        audioManager?.unregisterAudioRecordingCallback(callback)
    }

    @RequiresApi(Build.VERSION_CODES.R)
    fun isMicrophoneInUseByOtherApp(): Boolean {
        return try {
            val audioManager = GlobalContext.instance.getSystemService(Context.AUDIO_SERVICE) as AudioManager
            // 检查是否有其他应用正在使用麦克风
            audioManager.activeRecordingConfigurations.isNotEmpty()
        } catch (e: Exception) {
            Logger.d("ShowAudioRecorder", "检查活动录音配置异常: ${e.message}")
            false
        }
    }

    fun isScreenOn(context: Context): Boolean {
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        return powerManager.isInteractive
    }

}
