package com.tcl.ai.note.voicetotext.view.widget

import android.annotation.SuppressLint
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Slider
import androidx.compose.material3.SliderDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.voicetotext.R
import com.tcl.ai.note.voicetotext.states.RecordPlayingState
import com.tcl.ai.note.voicetotext.util.formatAudioTime
import com.tcl.ai.note.voicetotext.util.formatAudioTimeHourMinuteSecond
import com.tcl.ai.note.voicetotext.util.formatAudioTimeMinuteSecond
import com.tcl.ai.note.voicetotext.util.getAudioDuration
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.voicetotext.vm.RecordingViewModel
import com.tcl.ai.note.widget.HorizontalLine
import com.tcl.ai.note.widget.HoverProofIconButton
import java.io.File


/**
 * 录音内容块,显示在标题栏下方
 * */
@Composable
fun TabletTopAudioBlock(
    noteId: Long,
    darkTheme: Boolean =false,
    audioPaths: List<String>,
    showPanel: Boolean = true,
    recordPlayingState: RecordPlayingState,
    onSeek: (Int, String) -> Unit,
    onPlayClick: (String) -> Unit,
    onPauseClick: () -> Unit,
    onAudioToTextClick: () -> Unit,
    onConfirmRename: (String, String) -> Unit,
    onDeleteAudios: (List<String>) -> Unit,
    onStartRecordingClick:() -> Unit,
    recordingViewModel: RecordingViewModel,
    audioToTextViewModel: AudioToTextViewModel,
) {
    if (audioPaths.isNotEmpty()) {
        val recordingState by recordingViewModel.recordState.collectAsState()
        val isPanelVisible by remember(showPanel, recordingState) {
            mutableStateOf(if (!recordingState.isRecording) {
                showPanel
            } else {
                false
            })
        }
        val dimens = getGlobalDimens()
        val latestAudioFile by remember(audioPaths) { mutableStateOf(File(audioPaths.last())) }
        var showedAudioFile by remember { mutableStateOf(File(audioPaths.first())) } // 初始显示第一个录音
        var isInitialLoad by remember { mutableStateOf(true) } // 跟踪是否是初始加载
        val isCurrentPlaying = recordPlayingState.isPlaying && recordPlayingState.audioPath == showedAudioFile.absolutePath
        var totalDuration by remember(isPanelVisible, showedAudioFile) { mutableLongStateOf(getAudioDuration(showedAudioFile.absolutePath)) }
        var playingProgress by remember(recordPlayingState) { mutableLongStateOf(
            if (isCurrentPlaying)
                recordPlayingState.playingPosition.toLong()
            else
                0L
        ) }
        var totalDurationText by remember(totalDuration) {
            mutableStateOf(isTablet.judge(formatAudioTime(totalDuration), formatAudioTimeHourMinuteSecond(totalDuration)))
        }
        var playingText by remember(recordPlayingState, showedAudioFile) { mutableStateOf(isTablet.judge(formatAudioTime(playingProgress), formatAudioTimeMinuteSecond(playingProgress))) }

        var showRecordingList by remember { mutableStateOf(false) }

        val importAudioHelper = rememberAudioPermissionHelper(
            noteId = noteId,
            viewModel = recordingViewModel,
            onStartRecording = {
                onStartRecordingClick()
            }
        )

        val paddingHorizontal = isTablet.judge(24.dp, 16.dp)

        LaunchedEffect(audioPaths, recordingState) {
            if (isInitialLoad) {
                // 初始加载时显示第一个录音（最早的录音）
                Logger.d("TabletTopAudioBlock", "Initial load: showing first audio ${audioPaths.first()}")
                showedAudioFile = File(audioPaths.first())
                isInitialLoad = false
            } else {
                // 非初始加载时的更新条件：
                // 1. 当前显示的文件不存在
                // 2. 正在录音时
                // 3. 录音刚停止时，切换到最新录音
                if (!showedAudioFile.exists() || recordingState.isRecording ||
                    (!recordingState.isRecording && showedAudioFile.absolutePath != latestAudioFile.absolutePath)) {
                    Logger.d("TabletTopAudioBlock", "Updating showedAudioFile from ${showedAudioFile.absolutePath} to ${latestAudioFile.absolutePath}")
                    showedAudioFile = latestAudioFile
                }
            }
            playingText = isTablet.judge(formatAudioTime(0L), formatAudioTimeMinuteSecond(0L))
            playingProgress = 0
        }

        AnimatedVisibility(
            visible = isPanelVisible,
            enter = expandVertically(),
            exit = shrinkVertically()
        ) {
            Box {
                HorizontalLine(modifier = Modifier.align(alignment = Alignment.TopCenter))
                Row(modifier = Modifier
                    .fillMaxWidth()
                    .height(dimens.menuBarHeight)
                    .padding(horizontal = paddingHorizontal),
                    verticalAlignment = Alignment.CenterVertically,) {

                    if (isCurrentPlaying) {
                        HoverProofIconButton(
                            modifier = Modifier
                                .padding(end = 16.dp)
                                .size(32.dp),
                            enabled = true,
                            onClick = {
                                onPauseClick()
                            }
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_audio_top_bar_pause),
                                contentDescription = stringResource(com.tcl.ai.note.base.R.string.audio_pause),
                            )
                        }
                    } else {
                        HoverProofIconButton(
                            modifier = Modifier
                                .padding(end = 16.dp)
                                .size(32.dp),
                            enabled = true,
                            onClick = {
                                onPlayClick(showedAudioFile.absolutePath)
                            }
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_audio_top_bar_play),
                                contentDescription = stringResource(com.tcl.ai.note.base.R.string.audio_play),
                            )
                        }
                    }

                    if (isTablet) {
                        Text(
                            text = showedAudioFile.nameWithoutExtension,
                            fontSize = 14.sp,
                            color = TclTheme.colorScheme.tctStanderTextPrimary,
                            modifier = Modifier
                                .padding(end = 16.dp)
                                .width(90.dp),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }

                    Text(
                        text = playingText,
                        fontSize = isTablet.judge(14.sp,12.sp),
                        lineHeight = isTablet.judge(14.sp,12.sp),
                        color = TclTheme.colorScheme.tctStanderTextPrimary,
                        modifier = Modifier
                            .wrapContentWidth()
                    )

                    AudioSeekBar(
                        modifier = Modifier
                            .weight(1f, fill = false)
                            .padding(horizontal = 8.dp),
                        progress = playingProgress,
                        duration = totalDuration,
                        onValueChange = { seekTo ->
                            playingText = isTablet.judge(formatAudioTime(seekTo.toLong()), formatAudioTimeMinuteSecond(seekTo.toLong()))
//                            onSeek(seekTo.toInt(), showedAudioFile.absolutePath)
                        },
                        onSeek = { seekTo ->
                            onSeek(seekTo.toInt(), showedAudioFile.absolutePath)
                        }
                    )

                    Text(
                        text = totalDurationText,
                        fontSize = isTablet.judge(14.sp,12.sp),
                        lineHeight = isTablet.judge(14.sp,12.sp),
                        color = TclTheme.colorScheme.tctStanderTextSecondary,
                        modifier = Modifier
                            .wrapContentWidth()
                            .padding(end = 16.dp)
                    )

                    HoverProofIconButton(
                        modifier = Modifier.size(dimens.iconSize),
                        enabled = true,
                        onClick = {
                            importAudioHelper.invoke {  }
                        }
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_start_recording),
                            contentDescription = stringResource(com.tcl.ai.note.base.R.string.accessibility_start_new_recording),
                            modifier = Modifier.size(dimens.iconSize)
                        )
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    HoverProofIconButton(
                        modifier = Modifier.size(dimens.btnSize),
                        enabled = true,
                        onClick = {
                            showRecordingList = !showRecordingList
                        }
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_recording_list_more),
                            contentDescription = stringResource(com.tcl.ai.note.base.R.string.accessibility_show_recording_list),
                            modifier = Modifier.size(dimens.iconSize)
                        )
                    }
                }
                if(!isTablet){
                    HorizontalLine(modifier = Modifier.align(alignment = Alignment.BottomCenter))
                }
            }
        }

        if (showRecordingList) {
            RecordingList(
                noteId = noteId,
                audioPaths = audioPaths,
                currentDisplayedAudioPath = showedAudioFile.absolutePath,
                onDeleteAudios = { deletedAudioPaths ->
                    onDeleteAudios(deletedAudioPaths)
                },
                onDismissRequest = {
                    showRecordingList = false
                },
                onAudioToTextClick = {
                    onAudioToTextClick()
                },
                onItemClick = { audioPath ->
                    Logger.d("AudioToTextScreen", "onItemClick: $audioPath")
                    showedAudioFile = File(audioPath)
                    totalDuration = getAudioDuration(audioPath)
                    totalDurationText = isTablet.judge(formatAudioTime(totalDuration), formatAudioTimeMinuteSecond(totalDuration))
                    playingText = isTablet.judge(formatAudioTime(playingProgress) , formatAudioTimeMinuteSecond(playingProgress))
                    onPlayClick(showedAudioFile.absolutePath)
                },
                onConfirmRename = { oldAudioPath, newAudioPath ->
                    onConfirmRename(oldAudioPath, newAudioPath)
                    if (oldAudioPath == showedAudioFile.absolutePath) {
                        showedAudioFile = File(newAudioPath)
                    }
                },
                audioToTextViewModel = audioToTextViewModel,

            )
        }
    }
}

@SuppressLint("DesignSystem")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AudioSeekBar(
    modifier: Modifier = Modifier,
    progress: Long,
    duration: Long,
    onValueChange: (Float) -> Unit,
    onSeek: (Float) -> Unit,
    valueRange: ClosedFloatingPointRange<Float> = 0f..duration.toFloat(),
) {
    val interactionSource = remember { MutableInteractionSource() }
    var isUserDragging by remember { mutableStateOf(false) }
    var sliderPosition by remember(isUserDragging) { mutableFloatStateOf(progress.toFloat()) }
    val isDarkTheme = isSystemInDarkTheme()
    val thumbBackgroundColor = if (isDarkTheme)  0xFF1A1A1A else 0xFFFFFFFF
    val thumbStrokeColor = if (isDarkTheme)  0xFFFFFFFF else 0xFF1A1A1A
    val context = LocalContext.current

    // 添加动画状态，控制滑动条高度变化
    var targetTrackHeight by remember { mutableFloatStateOf(2f) }
    val animatedTrackHeight by animateFloatAsState(
        targetValue = targetTrackHeight,
        animationSpec = tween(durationMillis = 300, easing = androidx.compose.animation.core.FastOutSlowInEasing),
        label = "TrackHeightAnimation"
    )

    // 监听拖动状态变化
    LaunchedEffect(isUserDragging) {
        targetTrackHeight = if (isUserDragging) {
            8f // 拖动时高度变为6dp
        } else {
            2f // 停止拖动时恢复为2dp
        }
    }

    // 当progress变化时更新sliderPosition（但仅当用户没有拖动滑块时）
    LaunchedEffect(progress, isUserDragging) {
        if (!isUserDragging) {
            sliderPosition = progress.toFloat()
        }
    }

    // 当progress变化时更新sliderPosition（但仅当用户没有拖动滑块时）
    LaunchedEffect(sliderPosition, isUserDragging) {
        if (!isUserDragging) {
            sliderPosition = progress.toFloat()
        }
    }

    Slider(
        interactionSource = interactionSource,
        value = if (isUserDragging) sliderPosition else progress.toFloat(),
        onValueChange = {
            isUserDragging = true
            sliderPosition = it
            onValueChange(sliderPosition)
        },
        onValueChangeFinished = {
            onSeek(sliderPosition)
            isUserDragging = false
        },
        valueRange = valueRange,
        modifier = modifier
            .padding(0.dp)
            .semantics {
                contentDescription = context.getString(com.tcl.ai.note.base.R.string.accessibility_audio_seek_bar)
            },
        enabled = true, //禁止/使能拖动
        colors = SliderDefaults.colors(
            thumbColor = Color.Green,
            activeTrackColor = TclTheme.colorScheme.tctStanderTextPrimary,
            inactiveTrackColor = TclTheme.tclColorScheme.tctStanderTextSecondary
        ),
        thumb = {
            val thumbRadius = 8.dp // 总半径为8dp，使thumb总宽度为16dp
            SliderDefaults.Thumb(
                interactionSource = interactionSource,
                modifier = Modifier
                    .size(0.dp)
                    .offset(x = 0.dp, y = thumbRadius)
                    .drawBehind {
                        // 计算thumb的实际位置，确保平滑移动且在初始状态下能覆盖track左侧
                        val progressRatio = if (duration > 0) (progress.toFloat() / duration.toFloat()).coerceIn(0f, 1f) else 0f
                        val trackWidth = size.width
                        val thumbRadiusPx = thumbRadius.toPx()

                        // 计算thumb应该在的位置：从左边缘的thumb半径位置开始，到右边缘的thumb半径位置结束
                        val thumbCenterX = when {
                            progressRatio <= 0f -> thumbRadiusPx // 初始状态，thumb中心在半径位置
                            progressRatio >= 1f -> trackWidth - thumbRadiusPx // 结束状态，thumb中心在右侧半径位置
                            else -> {
                                // 中间状态：在可移动范围内线性插值
                                val moveableRange = trackWidth - 2 * thumbRadiusPx
                                thumbRadiusPx + moveableRange * progressRatio
                            }
                        }

                        // 先绘制白色填充圆，作为thumb的背景
                        drawCircle(
                            color = Color(thumbBackgroundColor),
                            radius = thumbRadiusPx,
                            center = Offset(
                                x = thumbCenterX,
                                y = center.y
                            )
                        )

                        // 再绘制黑色边框圆环，stroke宽度为1dp
                        drawCircle(
                            color = Color(thumbStrokeColor),
                            radius = thumbRadiusPx,
                            style = Stroke(width = 1.dp.toPx()),
                            center = Offset(
                                x = thumbCenterX,
                                y = center.y
                            )
                        )
                    },
            )
        },
        track = { sliderState ->
            // 在Composable上下文中获取颜色值
            val activeColor = TclTheme.colorScheme.tctStanderTextPrimary
            val inactiveColor = TclTheme.colorScheme.seekBarColor
            val currentPosition = if (isUserDragging) sliderPosition else progress.toFloat()
            // 自定义track确保activeTrackColor从最左侧开始
            Box(
                modifier = Modifier
                    .height(animatedTrackHeight.dp)
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(2.dp))
                    .drawBehind {
                        val baseTrackHeight = 2.dp.toPx() // 基础高度
                        val currentTrackHeight = animatedTrackHeight.dp.toPx() // 当前动画高度
                        val trackWidth = size.width

                        // 计算垂直方向的偏移量，实现从中间向上下扩展
                        val verticalOffset = (currentTrackHeight - baseTrackHeight) / 2

                        val progressRatio = if (duration > 0) (currentPosition / duration.toFloat()).coerceIn(0f, 1f) else 0f
                        val thumbRadiusPx = 8.dp.toPx()

                        // 计算activeWidth，与thumb位置保持一致
                        val activeWidth = when {
                            progressRatio <= 0f -> thumbRadiusPx // 初始状态，active宽度等于thumb半径
                            progressRatio >= 1f -> trackWidth // 结束状态，active覆盖整个track
                            else -> {
                                // 中间状态：active宽度到thumb中心位置
                                val moveableRange = trackWidth - 2 * thumbRadiusPx
                                thumbRadiusPx + moveableRange * progressRatio
                            }
                        }

                        // 绘制完整的背景track
                        drawRect(
                            color = inactiveColor,
                            topLeft = Offset(0f, (size.height - currentTrackHeight) / 2),
                            size = Size(trackWidth, currentTrackHeight)
                        )

                        // 绘制active部分（带垂直扩展效果）
                        drawRect(
                            color = activeColor,
                            topLeft = Offset(0f, (size.height - currentTrackHeight) / 2),
                            size = Size(activeWidth, currentTrackHeight)
                        )
                    }
            )
        }
    )
}


@Composable
@Preview
private fun TabletTopAudioBlockPreview() {

}
