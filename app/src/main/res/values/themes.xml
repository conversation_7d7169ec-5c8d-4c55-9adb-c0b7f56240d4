<?xml version="1.0" encoding="utf-8"?>
<resources>

<!--    <style name="Theme.Note" parent="android:Theme.Material.Light.NoActionBar" />-->

<!--    <style name="TransparentTheme" parent="Theme.Note">-->
<!--        <item name="android:windowIsTranslucent">true</item>-->
<!--        <item name="android:windowBackground">@android:color/transparent</item>-->
<!--        <item name="android:windowContentOverlay">@null</item>-->
<!--        <item name="android:windowNoTitle">true</item>-->
<!--        <item name="android:backgroundDimEnabled">true</item>-->
<!--    </style>-->

<!--    <style name="Theme.Note.DialogStyle" parent="Base.Theme.AppCompat.Dialog">-->
<!--        &lt;!&ndash;设置没有ActionBar&ndash;&gt;-->
<!--        <item name="windowActionBar">false</item>-->
<!--        <item name="windowNoTitle">true</item>-->
<!--        &lt;!&ndash; Dialog的windowFrame框 &ndash;&gt;-->
<!--        <item name="android:windowFrame">@null</item>-->
<!--        &lt;!&ndash; 是否浮现在activity之上 &ndash;&gt;-->
<!--        <item name="android:windowIsFloating">true</item>-->
<!--        &lt;!&ndash; 是否半透明 &ndash;&gt;-->
<!--        <item name="android:windowIsTranslucent">false</item>-->
<!--        &lt;!&ndash; 是否显示title &ndash;&gt;-->
<!--        <item name="android:windowNoTitle">true</item>-->
<!--        &lt;!&ndash; Dialog的背景 &ndash;&gt;-->
<!--        <item name="android:windowBackground">@android:color/transparent</item>-->
<!--        &lt;!&ndash; 背景是否模糊显示 &ndash;&gt;-->
<!--        <item name="android:backgroundDimEnabled">true</item>-->
<!--        <item name="android:windowCloseOnTouchOutside">false</item>-->
<!--    </style>-->

<!--    <style name="TransparentLauncher" parent="android:Theme">-->
<!--        <item name="android:windowIsTranslucent">true</item>-->
<!--        <item name="android:windowBackground">@android:color/transparent</item>-->
<!--        <item name="android:windowContentOverlay">@null</item>-->
<!--        <item name="android:windowNoTitle">true</item>-->
<!--        <item name="android:windowIsFloating">true</item>-->
<!--        <item name="android:backgroundDimEnabled">false</item>-->
<!--    </style>-->
</resources>