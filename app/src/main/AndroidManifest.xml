<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="com.tcl.ai.ability.actions.GET_ASSISTANT" />



    <application
        android:name=".MyApplication"
        android:allowBackup="false"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_note_icon"
        android:networkSecurityConfig="@xml/network_security_config"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_note_icon_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Note"
        tools:replace="android:allowBackup,android:theme">
        <meta-data
            android:name="user_id"
            android:value="584f9c5bab31fb1d59e138e1" />
        <meta-data
            android:name="project_id"
            android:value="28575a96052d425e9fa2fc606ea6905c" />
        <meta-data
            android:name="channel_Name"
            android:value="all" />

    </application>

</manifest>