# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-keep class com.tcl.componentfrm.pubapi.* { *; }
-keep class bi.com.tcl.bi.TTVSReporter
-keep class com.tcl.bi.plugin.bean.** { *; }
-keep class bi.com.tcl.bi.bean.** { *; }
-keep class com.tcl.ai.note.controller.AccountController
-keep class * extends com.tcl.ff.component.core.http.api.BaseApi{*;}
-keep public class com.tcl.ff.component.core.http.** {
    public *;
    protected *;
}
-keep class com.thoughtworks.xstream.** { *; }
-keep class org.xmlpull.v1.** { *; }
-keep class io.reactivex.** { *; }
-keep class retrofit2.** { *; }
-keep class okhttp3.** { *; }
-keep class okio.** { *; }

-keep class bi.com.tcl.bi.room.** {*;}
-keep class com.tcl.ttvs.** { *; }

-keep class com.tcl.ai.note.handwritingtext.database.entity.NoteEntity { *; }
-keep class com.tcl.ai.note.handwritingtext.database.entity.RawStroke { *; }
-keep class com.tcl.ai.note.handwritingtext.database.entity.RawPoint { *; }
-keep class com.jideos.predengine.PointBean { *; }
-keep class com.jideos.predengine.JidePredManager{ *; }
# To avoid warnings regarding Kotlin classes reflection
-dontwarn kotlin.**
-keep class kotlin.** { *; }
-keepclassmembers class kotlin.** { *; }
-keepclassmembers enum kotlin.** { *; }
-keepclassmembers class **.kotlin.Metadata { *; }

# Kotlin 反射支持
-keep class kotlin.reflect.** { *; }
-keepclassmembers class kotlin.Metadata { *; }

# 保留生成的数据类适配器
-keep class **JsonAdapter { *; }
-keep class **$$JsonAdapter { *; }

# 保留类型信息
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# Kotlin元数据保留
-keepclassmembers class **$.WhenMappings { *; }
-keepclassmembers class * {
    @org.jetbrains.annotations.NotNull *;
    @org.jetbrains.annotations.Nullable *;
}

# 保留kotlin协程相关
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}

# 四大组件
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.view.View

# sunia
-keep class com.sunia.** { *; }

# 富文本
-keep public class com.tcl.ai.note.handwritingtext.richtext.styles.** { *; }

# Google Mediapipe
# 保留所有protobuf生成的类及其成员
-keep class com.google.mediapipe.proto.** { *; }
# 保留protobuf消息类的所有成员
-keepclassmembers class * extends com.google.protobuf.GeneratedMessageLite { *; }
# 保留Flogger日志框架的所有类
-keep class com.google.common.flogger.** { *; }

# 可选：保留Gson/Fastjson相关的反射（核心！）
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class com.google.gson.Gson { *; }
-keep class com.google.gson.** { *; }
-keep class com.tcl.ai.note.template.bean.PageConfigInfo { *; }
-keep class com.tcl.ai.note.template.bean.**  { *; }
-keep,allowobfuscation @interface androidx.annotation.Keep
-keep @androidx.annotation.Keep class * {*;}
-keepclassmembers class com.tcl.ai.note.template.bean.** {
    <init>(...);
    <fields>;
}
-keep class com.tcl.ai.note.picturetotext.bean.** { *; }
# Fastjson如果用到了
-keep class com.alibaba.fastjson.** { *; }

-dontwarn android.media.AudioAttributes
-dontwarn com.android.internal.view.menu.MenuBuilder$Callback
-dontwarn com.android.internal.view.menu.MenuBuilder
-dontwarn com.android.internal.view.menu.MenuPresenter$Callback
-dontwarn com.android.internal.widget.DecorToolbar
-dontwarn com.bea.xml.stream.MXParserFactory
-dontwarn com.bea.xml.stream.XMLOutputFactoryBase
-dontwarn com.ctc.wstx.stax.WstxInputFactory
-dontwarn com.ctc.wstx.stax.WstxOutputFactory
-dontwarn com.tcl.ff.component.core.http.HttpApi
-dontwarn com.tcl.ff.component.rxlifecycle.LifecycleTransformer
-dontwarn java.awt.Color
-dontwarn java.awt.Font
-dontwarn java.beans.BeanInfo
-dontwarn java.beans.IntrospectionException
-dontwarn java.beans.Introspector
-dontwarn java.beans.PropertyDescriptor
-dontwarn java.beans.PropertyEditor
-dontwarn javax.activation.ActivationDataFlavor
-dontwarn javax.swing.plaf.FontUIResource
-dontwarn javax.xml.bind.DatatypeConverter
-dontwarn javax.xml.stream.Location
-dontwarn javax.xml.stream.XMLInputFactory
-dontwarn javax.xml.stream.XMLOutputFactory
-dontwarn javax.xml.stream.XMLStreamException
-dontwarn javax.xml.stream.XMLStreamReader
-dontwarn javax.xml.stream.XMLStreamWriter
-dontwarn net.sf.cglib.proxy.Callback
-dontwarn net.sf.cglib.proxy.CallbackFilter
-dontwarn net.sf.cglib.proxy.Enhancer
-dontwarn net.sf.cglib.proxy.Factory
-dontwarn net.sf.cglib.proxy.NoOp
-dontwarn net.sf.cglib.proxy.Proxy
-dontwarn nu.xom.Attribute
-dontwarn nu.xom.Builder
-dontwarn nu.xom.Document
-dontwarn nu.xom.Element
-dontwarn nu.xom.Elements
-dontwarn nu.xom.Node
-dontwarn nu.xom.ParentNode
-dontwarn nu.xom.ParsingException
-dontwarn nu.xom.Text
-dontwarn nu.xom.ValidityException
-dontwarn okhttp3.internal.annotations.EverythingIsNonNull
-dontwarn org.codehaus.jettison.AbstractXMLStreamWriter
-dontwarn org.codehaus.jettison.mapped.Configuration
-dontwarn org.codehaus.jettison.mapped.MappedNamespaceConvention
-dontwarn org.codehaus.jettison.mapped.MappedXMLInputFactory
-dontwarn org.codehaus.jettison.mapped.MappedXMLOutputFactory
-dontwarn org.dom4j.Attribute
-dontwarn org.dom4j.Branch
-dontwarn org.dom4j.Document
-dontwarn org.dom4j.DocumentException
-dontwarn org.dom4j.DocumentFactory
-dontwarn org.dom4j.Element
-dontwarn org.dom4j.io.OutputFormat
-dontwarn org.dom4j.io.SAXReader
-dontwarn org.dom4j.io.XMLWriter
-dontwarn org.dom4j.tree.DefaultElement
-dontwarn org.jdom.Attribute
-dontwarn org.jdom.Content
-dontwarn org.jdom.DefaultJDOMFactory
-dontwarn org.jdom.Document
-dontwarn org.jdom.Element
-dontwarn org.jdom.JDOMException
-dontwarn org.jdom.JDOMFactory
-dontwarn org.jdom.Text
-dontwarn org.jdom.input.SAXBuilder
-dontwarn org.jdom2.Attribute
-dontwarn org.jdom2.Content
-dontwarn org.jdom2.DefaultJDOMFactory
-dontwarn org.jdom2.Document
-dontwarn org.jdom2.Element
-dontwarn org.jdom2.JDOMException
-dontwarn org.jdom2.JDOMFactory
-dontwarn org.jdom2.Text
-dontwarn org.jdom2.input.SAXBuilder
-dontwarn org.joda.time.DateTime
-dontwarn org.joda.time.DateTimeZone
-dontwarn org.joda.time.format.DateTimeFormatter
-dontwarn org.joda.time.format.ISODateTimeFormat
-dontwarn org.kxml2.io.KXmlParser
-dontwarn org.xmlpull.mxp1.MXParser

-dontwarn com.android.internal.app.IAppOpsService
-dontwarn com.android.internal.app.IBatteryStats
-dontwarn com.android.internal.app.IVoiceInteractor
-dontwarn com.android.internal.appwidget.IAppWidgetService
-dontwarn com.android.internal.compat.IPlatformCompat
-dontwarn com.android.internal.inputmethod.IInputContentUriToken
-dontwarn com.android.internal.inputmethod.IInputMethodSession
-dontwarn com.android.internal.os.BinderCallHeavyHitterWatcher$BinderCallHeavyHitterListener
-dontwarn com.android.internal.os.BinderInternal$Observer
-dontwarn com.android.internal.os.BinderInternal$WorkSourceProvider
-dontwarn com.android.internal.os.IDropBoxManagerService
-dontwarn com.android.internal.os.IResultReceiver
-dontwarn com.android.internal.telecom.ITelecomService
-dontwarn com.android.internal.telephony.CellNetworkScanResult
-dontwarn com.android.internal.telephony.IPhoneSubInfo
-dontwarn com.android.internal.telephony.ISms
-dontwarn com.android.internal.telephony.ISub
-dontwarn com.android.internal.telephony.ITelephony
-dontwarn com.android.internal.telephony.OperatorInfo
-dontwarn com.android.internal.textservice.ISpellCheckerSessionListener
-dontwarn com.android.internal.util.FunctionalUtils$ThrowingRunnable
-dontwarn com.android.internal.util.FunctionalUtils$ThrowingSupplier
-dontwarn com.android.internal.util.IndentingPrintWriter
-dontwarn com.android.internal.util.XmlUtils$WriteMapCallback
-dontwarn com.android.internal.util.function.LongObjPredicate
-dontwarn com.android.internal.view.IDragAndDropPermissions
-dontwarn com.android.internal.view.IInputMethodManager
-dontwarn com.android.modules.utils.TypedXmlPullParser
-dontwarn com.android.modules.utils.TypedXmlSerializer
-dontwarn libcore.util.NativeAllocationRegistry

-dontwarn javax.lang.model.SourceVersion
-dontwarn javax.lang.model.element.Element
-dontwarn javax.lang.model.element.ElementKind
-dontwarn javax.lang.model.element.Modifier
-dontwarn javax.lang.model.type.TypeMirror
-dontwarn javax.lang.model.type.TypeVisitor
-dontwarn javax.lang.model.util.SimpleTypeVisitor8