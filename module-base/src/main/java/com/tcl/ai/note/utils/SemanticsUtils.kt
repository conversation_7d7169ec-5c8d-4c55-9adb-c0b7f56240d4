package com.tcl.ai.note.utils

import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R

/**
 * 无障碍语义工具类
 * 用于统一处理各种控件的无障碍描述
 * 谁要添加定制程度更高类型的，可以进行仿写
 */
object SemanticsUtils {
    
    /**
     * 为控件添加标准的无障碍语义
     * @param content 内容描述（如"红色"、"背景样式1"等）
     * @param controlType 控件类型（如"按钮"、"复选框"等）
     * @param isSelected 是否选中状态
     * @param role 语义角色
     * @return 带有完整无障碍描述的 Modifier
     */
    fun Modifier.accessibilitySemantics(
        content: String,
        controlType: String,
        isSelected: Boolean? = null,
        role: Role = Role.Button
    ): Modifier = this.semantics {
        val context = GlobalContext.appContext
        
        val separator = context.getString(R.string.accessibility_separator)
        
        // 构建完整的无障碍描述
        val stateDesc = isSelected?.let { selected ->
            if (selected) {
                context.getString(R.string.checked_status)
            } else {
                context.getString(R.string.unchecked_status)
            }
        }
        
        // 已选中的控件不需要激活提示，未选中的才需要
        val actionDesc = if (isSelected == true) {
            null // 已选中时不提供激活提示
        } else {
            context.getString(R.string.double_tap_to_activate)
        }
        
        // 使用格式化字符串构建描述
        this.contentDescription = when {
            stateDesc != null && actionDesc != null -> {
                // 有状态且有操作提示：内容+类型+状态+操作
                context.getString(
                    R.string.accessibility_description_format,
                    content,
                    controlType,
                    stateDesc,
                    actionDesc
                )
            }
            stateDesc != null -> {
                // 有状态但无操作提示（已选中）：内容+类型+状态
                context.getString(
                    R.string.accessibility_description_format_3_parts,
                    content,
                    controlType,
                    stateDesc
                )
            }
            else -> {
                // 没有状态信息但有操作提示：内容+类型+操作提示
                context.getString(
                    R.string.accessibility_description_format_3_parts,
                    content,
                    controlType,
                    actionDesc!!
                )
            }
        }
        
        this.role = role
    }
    
    /**
     * 为按钮添加无障碍语义的便捷方法
     */
    fun Modifier.buttonSemantics(
        content: String,
        isSelected: Boolean? = null
    ): Modifier {
        val context = GlobalContext.appContext
        return accessibilitySemantics(
            content = content,
            controlType = context.getString(R.string.button_type_semantics),
            isSelected = isSelected,
            role = Role.Button
        )
    }
    
    /**
     * 为复选框添加无障碍语义的便捷方法
     */
    fun Modifier.checkboxSemantics(
        content: String,
        isSelected: Boolean
    ): Modifier {
        val context = GlobalContext.appContext
        return accessibilitySemantics(
            content = content,
            controlType = context.getString(R.string.checkbox_type_semantics),
            isSelected = isSelected,
            role = Role.Checkbox
        )
    }
    
    /**
     * 为选项卡添加无障碍语义的便捷方法
     */
    fun Modifier.tabSemantics(
        content: String,
        isSelected: Boolean
    ): Modifier {
        val context = GlobalContext.appContext
        return accessibilitySemantics(
            content = content,
            controlType = context.getString(R.string.tab_type_semantics),
            isSelected = isSelected,
            role = Role.Tab
        )
    }
    
    /**
     * 为单选按钮添加无障碍语义的便捷方法
     */
    fun Modifier.radioButtonSemantics(
        content: String,
        isSelected: Boolean
    ): Modifier {
        val context = GlobalContext.appContext
        return accessibilitySemantics(
            content = content,
            controlType = context.getString(R.string.radio_button_type_semantics),
            isSelected = isSelected,
            role = Role.RadioButton
        )
    }
    
    /**
     * 为开关添加无障碍语义的便捷方法
     */
    fun Modifier.switchSemantics(
        content: String,
        isEnabled: Boolean
    ): Modifier {
        val context = GlobalContext.appContext
        return accessibilitySemantics(
            content = content,
            controlType = context.getString(R.string.switch_type_semantics),
            isSelected = isEnabled,
            role = Role.Switch
        )
    }
    
    /**
     * 为滑块添加无障碍语义的便捷方法
     * 注意：Compose没有专门的Slider Role，对于真正的滑块建议直接使用Compose的Slider组件
     * 这个方法主要用于自定义的类滑块控件
     */
    fun Modifier.sliderSemantics(
        content: String,
        value: Float? = null,
        valueRange: ClosedFloatingPointRange<Float>? = null
    ): Modifier {
        val context = GlobalContext.appContext
        val controlType = context.getString(R.string.slider_type_semantics)
        
        // 如果有数值信息，包含在描述中
        val fullContent = if (value != null && valueRange != null) {
            val percentage = ((value - valueRange.start) / (valueRange.endInclusive - valueRange.start) * 100).toInt()
            "$content, $percentage%"
        } else {
            content
        }
        
        // 滑块通常不需要"点按两次激活"的提示，因为它们有自己的交互方式（拖拽）
        return this.semantics {
            this.contentDescription = "${fullContent}${context.getString(R.string.accessibility_separator)}$controlType"
            // 不设置特定的role，让滑块组件自己处理语义
        }
    }
}

