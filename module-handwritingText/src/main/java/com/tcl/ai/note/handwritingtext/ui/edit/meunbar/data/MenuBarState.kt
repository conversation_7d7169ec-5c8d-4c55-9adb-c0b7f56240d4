package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.data

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.tcl.ai.note.handwritingtext.bean.PenStyle
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.provider.MenuBarUiState
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.provider.RichTextToolBarState
import com.tcl.ai.note.handwritingtext.vm.state.RichTextDataState

/**
 * 菜单栏状态管理类
 * 集中管理所有状态的收集和转换逻辑
 */
class MenuBarStateManager(
    private val dependencies: MenuBarDependencies
) {
    
    /**
     * 收集所有状态并返回组合状态
     */
    @Composable
    fun collectStates(): MenuBarStates {
        // 原始状态收集
        val editMode by dependencies.textAndDrawViewModel.editModeState.collectAsState()
        val menuBarState by dependencies.menuBarViewModel.menuBarState.collectAsState()
        val toolBarState by dependencies.richTextToolBarViewModel.toolBarState.collectAsState()
        val canDrawUndo by dependencies.textAndDrawViewModel.canUndoState.collectAsState()
        val canDrawRedo by dependencies.textAndDrawViewModel.canRedoState.collectAsState()
        val selectedPen = dependencies.penToolbarViewModel.selectedPen  // 笔刷那边不是StateFlow
        val initCount = dependencies.penToolbarViewModel.initCount      // 笔刷那边不是StateFlow
        val isShowBottomAIPop by dependencies.richTextToolBarViewModel.isShowBottomAIPop.collectAsState()
        val noteUIState by dependencies.richTextViewModel.uiState.collectAsState()
        val fingerDrawEnabled by dependencies.suniaDrawViewModel.enableFingerDrawingState.collectAsState()

        // 转换为简化状态
        val simplifiedMenuBarState = MenuBarUiState(
            currentMenuType = menuBarState.currentMenuType,
            isKeyboardActive = menuBarState.isKeyboardActive,
            isBrushActive = menuBarState.isBrushActive,
            isEraserActive = menuBarState.isEraserActive,
            isLassoActive = menuBarState.isLassoActive
        )

        val simplifiedToolBarState = RichTextToolBarState(
            isTodoActive = toolBarState.isTodoActive,
            isCanUndo = toolBarState.isCanUndo,
            isCanRedo = toolBarState.isCanRedo
        )

        return MenuBarStates(
            editMode = editMode,
            menuBarState = simplifiedMenuBarState,
            toolBarState = simplifiedToolBarState,
            canDrawUndo = canDrawUndo,
            canDrawRedo = canDrawRedo,
            selectedPen = selectedPen,
            initCount = initCount,
            isShowBottomAIPop = isShowBottomAIPop,
            noteUIState = noteUIState,
            fingerDrawEnabled = fingerDrawEnabled
        )
    }
}

/**
 * 菜单栏组合状态数据类
 * 包含所有UI渲染需要的状态
 */
data class MenuBarStates(
    val editMode: EditMode,
    val menuBarState: MenuBarUiState,
    val toolBarState: RichTextToolBarState,
    val canDrawUndo: Boolean,
    val canDrawRedo: Boolean,
    val selectedPen: PenStyle,
    val initCount: Int,
    val isShowBottomAIPop: Boolean,
    val noteUIState: RichTextDataState,
    val fingerDrawEnabled: Boolean
) 