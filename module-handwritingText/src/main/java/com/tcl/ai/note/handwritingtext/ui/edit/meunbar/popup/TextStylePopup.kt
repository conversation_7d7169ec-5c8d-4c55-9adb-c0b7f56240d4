package com.tcl.ai.note.handwritingtext.ui.edit.meunbar.popup

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.AppBarDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.ui.popup.AnimatedPopupWithShadow
import com.tcl.ai.note.handwritingtext.ui.popup.SlideFromBottomPopup
import com.tcl.ai.note.handwritingtext.ui.utils.rememberKeyboardState
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.SemanticsUtils.buttonSemantics
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.widget.DelayedBackgroundIconButton
import com.tcl.ai.note.handwritingtext.R as HR

/**
 * 文本样式弹框
 * 显示在TextStyle图标正上方，包含文本格式化选项
 */
@Composable
fun TextStylePopup(
    menuBarItem: MenuBarItem,
    areaHeight:Int,
    onDismissRequest: () -> Unit,
    onBulletListClick: () -> Unit = {},
    onNumberListClick: () -> Unit = {},
    onBoldClick: () -> Unit = {},
    onUnderlineClick: () -> Unit = {},
    onItalicClick: () -> Unit = {},
    isBoldActive: Boolean = false,
    isUnderlineActive: Boolean = false,
    isItalicActive: Boolean = false,
    isBulletListActive: Boolean = false,
    isNumberListActive: Boolean = false,
    isAudioBlockVisible: Boolean = false, // 录音块是否显示
) {
    val dimens = getGlobalDimens()
    val popupWidth: Dp = 280.dp
    val popupHeight: Dp = 128.dp

    val density = LocalDensity.current
    val screenWidthDp = LocalConfiguration.current.screenWidthDp
    val screenHeightDp = LocalConfiguration.current.screenHeightDp

    // 获取键盘状态和菜单栏状态
    val keyboardState by rememberKeyboardState()

    // 计算X轴偏移量，弹框相对于屏幕水平居中
    val xOffset = with(density) {
        val screenWidth = screenWidthDp.dp.toPx()
        val popupWidth = popupWidth.toPx()

        // 屏幕水平居中：(屏幕宽度 - 弹窗宽度) / 2
        ((screenWidth - popupWidth) / 2).toInt()
    }



    val space = 8.dp
    val insets = AppBarDefaults.bottomAppBarWindowInsets
    val bottomInsetPx = insets.getBottom(LocalDensity.current)
    val bottomInsetDp = with(density) { bottomInsetPx.toDp()}
    val keyBoardHeight = keyboardState.isVisible.judge(
        keyboardState.height-bottomInsetDp,
        0.dp
    )

    val yOffset =areaHeight - (popupHeight + space+keyBoardHeight).toPx


    AnimatedPopupWithShadow(
        onDismissRequest = onDismissRequest,
        offset = IntOffset(xOffset, yOffset.toInt()),
    ) { closePopup ->
        Box(
            modifier = Modifier
                .width(popupWidth)
                .height(popupHeight)
                .background(
                    color = TclTheme.colorScheme.tertiaryBackground,
                    shape = RoundedCornerShape(20.dp)
                )
                .padding(horizontal = 12.dp, vertical = 16.dp)
        ) {
            Column {
                Spacer(modifier = Modifier.height(8.dp))
                // 标题
                Text(
                    text = stringResource(R.string.text_style_options),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Medium,
                    fontFamily = FontFamily.Default, // Roboto is default in Android
                    color = TclTheme.colorScheme.textDialogTitle,
                    modifier = Modifier.padding(start = 12.dp, bottom = 16.dp)
                )

                // 按钮行容器
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 左边区块背景
                    Box(
                        modifier = Modifier
                            .width(100.dp)
                            .height(48.dp)
                            .background(
                                color = TclTheme.colorScheme.itemBackground,
                                shape = RoundedCornerShape(12.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 无序列表按钮
                            DelayedBackgroundIconButton(
                                btnSize = TclTheme.dimens.btnSize,
                                iconSize = TclTheme.dimens.iconSize,
                                painter = painterResource(id = HR.drawable.ic_richtext_menu_bulleted),
                                isChecked = isBulletListActive,
                                contentDescription = stringResource(R.string.rich_text_bullet_list),
                                onClick = {
                                    onBulletListClick()
                                },
                                modifier = Modifier.buttonSemantics(
                                    content = stringResource(R.string.rich_text_bullet_list),
                                    isSelected = isBulletListActive
                                )
                            )

                            // 有序列表按钮
                            DelayedBackgroundIconButton(
                                btnSize = TclTheme.dimens.btnSize,
                                iconSize = TclTheme.dimens.iconSize,
                                painter = painterResource(id = HR.drawable.ic_richtext_menu_numbered),
                                isChecked = isNumberListActive,
                                contentDescription = stringResource(R.string.rich_text_number_list),
                                onClick = {
                                    onNumberListClick()
                                },
                                modifier = Modifier.buttonSemantics(
                                    content = stringResource(R.string.rich_text_number_list),
                                    isSelected = isNumberListActive
                                )
                            )
                        }
                    }

                    // 右边区块背景
                    Box(
                        modifier = Modifier
                            .width(144.dp)
                            .height(48.dp)
                            .background(
                                color = TclTheme.colorScheme.itemBackground,
                                shape = RoundedCornerShape(12.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 粗体按钮
                            DelayedBackgroundIconButton(
                                btnSize = TclTheme.dimens.btnSize,
                                iconSize = TclTheme.dimens.iconSize,
                                painter = painterResource(id = HR.drawable.ic_richtext_menu_bold),
                                isChecked = isBoldActive,
                                contentDescription = stringResource(R.string.rich_text_bold),
                                onClick = {
                                    onBoldClick()
                                },
                                modifier = Modifier.buttonSemantics(
                                    content = stringResource(R.string.rich_text_bold),
                                    isSelected = isBoldActive
                                )
                            )

                            // 下划线按钮
                            DelayedBackgroundIconButton(
                                btnSize = TclTheme.dimens.btnSize,
                                iconSize = TclTheme.dimens.iconSize,
                                painter = painterResource(id = HR.drawable.ic_richtext_menu_underline),
                                isChecked = isUnderlineActive,
                                contentDescription = stringResource(R.string.rich_text_underline),
                                onClick = {
                                    onUnderlineClick()
                                },
                                modifier = Modifier.buttonSemantics(
                                    content = stringResource(R.string.rich_text_underline),
                                    isSelected = isUnderlineActive
                                )
                            )

                            // 斜体按钮
                            DelayedBackgroundIconButton(
                                btnSize = TclTheme.dimens.btnSize,
                                iconSize = TclTheme.dimens.iconSize,
                                painter = painterResource(id = HR.drawable.ic_richtext_menu_italic),
                                isChecked = isItalicActive,
                                contentDescription = stringResource(R.string.rich_text_italic),
                                onClick = {
                                    onItalicClick()
                                },
                                modifier = Modifier.buttonSemantics(
                                    content = stringResource(R.string.rich_text_italic),
                                    isSelected = isItalicActive
                                )
                            )
                        }
                    }
                }
            }
        }
    }
}