package com.tcl.ai.note.handwritingtext.richtext.styles;

import static java.lang.Math.min;

import android.text.Editable;
import android.text.Spannable;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.text.style.StrikethroughSpan;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.handwritingtext.richtext.inner.Constants;
import com.tcl.ai.note.handwritingtext.richtext.inner.Util;
import com.tcl.ai.note.handwritingtext.richtext.listener.StyleStatusListener;
import com.tcl.ai.note.handwritingtext.richtext.spans.ListBulletSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.ListNumberSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan;
import com.tcl.ai.note.utils.Logger;
import com.tcl.ai.note.handwritingtext.richtext.utils.RichTextKTUtilsKt;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

/**
 * All Rights Reserved.
 *
 * <AUTHOR> Liu
 *
 */
public class ARE_ListBullet implements IARE_Style {

	private ImageView mListBulletImageView;

	private AREditText mEditText;

	private boolean isListBulletCheck = false;
	private boolean isListBulletValid = false;

	public static final Class<?> SPAN_CLASS = ListBulletSpan.class;

	public ARE_ListBullet() {
		super();
	}

	@Override
	public boolean getIsChecked() {
		return isListBulletCheck;
	}

	@Override
	public void setChecked(boolean isChecked) {
		isListBulletCheck = isChecked;
	}
	@Override
	public ImageView getImageView() {
		// Do nothing
		return mListBulletImageView;
	}
	@Override
	public void setEditText(AREditText editText) {
		this.mEditText = editText;
	}

	@Override
	public EditText getEditText() {
		return this.mEditText;
	}

	@Override
	public void updateCheckStatus(boolean checked) {
		setChecked(checked);
	}

	@Override
	public void setisValid(boolean isValid) {
		isListBulletValid = isValid;
	}

	@Override
	public boolean getIsValid() {
		return isListBulletValid;
	}

	@Override
	public void setListenerForImageView(final ImageView imageView) {
		imageView.setOnClickListener(new OnClickListener() {
			@Override
			public void onClick(View v) {

			}
		});
	}

	public void setListBullet() {
		if(!isListBulletValid) {
			return;
		}
		Logger.d("datahub, blackpoint_click");
		mEditText.stopUiSHowMonitor();
		isListBulletCheck = !isListBulletCheck;
		mEditText.updateParagraphCheckStatus(ARE_ListBullet.this, isListBulletCheck);
		mEditText.stopApplyMonitor();
		mEditText.stopStorageMonitor();
		mEditText.restoreDefaultLineSpace();
		Logger.d("yjh", "ARE_ListBullet begin onClick, spanSize=" + mEditText.getEditableText().getSpans(0,mEditText.getEditableText().length(),Object.class).length);
		int currentLine = Util.getCurrentCursorLine(mEditText);
		int start = Util.getThisLineStart(mEditText, currentLine);
		int end = Util.getThisLineEnd(mEditText, currentLine);

		Editable editable = mEditText.getText();

		//
		// Check if there is any ListNumberSpan first.
		// If there is ListNumberSpan, it means this case:
		// User has typed in:
		//
		// 1. aa
		// 2. bb
		// 3. cc
		//
		// Then user clicks the Bullet icon at 1 or 2 or any other item
		// He wants to change current ListNumberSpan to ListBulletSpan
		//
		// So it becomes:
		// For example: user clicks Bullet icon at 2:
		// 1. aa
		// * bb
		// 1. cc
		//
		// Note that "cc" has been restarted from 1

		int selectionStart = mEditText.getSelectionStart();
		int selectionEnd = mEditText.getSelectionEnd();
		ListNumberSpan[] listNumberSpans = editable.getSpans(selectionStart,
				selectionEnd, ListNumberSpan.class);
		UpcomingListSpan[] upcomingListSpans = editable.getSpans(selectionStart,
				selectionEnd,UpcomingListSpan.class);

		if (null != listNumberSpans && listNumberSpans.length > 0) {
			changeListNumberSpanToListBulletSpan(editable, listNumberSpans);
			triggerTextChange();
			Logger.d("yjh", "ARE_ListBullet change onClick, spanSize=" + mEditText.getEditableText().getSpans(0,mEditText.getEditableText().length(),Object.class).length);
			return;
		}else if(null != upcomingListSpans && upcomingListSpans.length > 0) {
			changeListUpcomingToBullet(editable,upcomingListSpans);
			triggerTextChange();
			return;
		}
		try {
			// -----case 有多行同时选中场景
			int selectionStartLine = Util.getLineBySelection(mEditText, selectionStart);
			int selectionEndLine = Util.getLineBySelection(mEditText, selectionEnd);
			if (selectionStart != -1 && selectionEnd != -1 && selectionStartLine < selectionEndLine) {
				Util.makeOrUnMarkMultiLineListSpanWithNoNum(editable, selectionStart, selectionEnd, ListBulletSpan.class);
				triggerTextChange();
				return;
			}
		} catch (Exception e) {
			Logger.d("ARE_ListBullet", "setListBullet error: " + e);
		}
		// -----case 有多行同时选中场景

		//
		// Normal cases
		//
		ListBulletSpan[] listBulletSpans = editable.getSpans(start,
				end, ListBulletSpan.class);
		if (null == listBulletSpans || listBulletSpans.length == 0) {
			//
			// Current line is not list item span
			// By clicking the image view, we should make it as
			// BulletListItemSpan
			// And ReOrder
			//
			// ------------- CASE 1 ---------------
			// Case 1:
			// Nothing types in, user just clicks the List image
			// For this case we need to mark it as BulletListItemSpan

			//
			// -------------- CASE 2 --------------
			// Case 2:
			// Before or after the current line, there are already
			// BulletListItemSpan have been made
			// Like:
			// 1. AAA
			// BBB
			// 1. CCC
			//
			// User puts cursor to the 2nd line: BBB
			// And clicks the List image
			// For this case we need to make current line as
			// BulletListItemSpan
			// And, we should also reOrder them as:
			//
			// 1. AAA
			// 2. BBB
			// 3. CCC
			//

			//
			// Case 2
			//
			// There are list item spans ahead current editing
			ListBulletSpan[] aheadListItemSpans = editable.getSpans(
					start - 2, start - 1, ListBulletSpan.class);
			if (null != aheadListItemSpans
					&& aheadListItemSpans.length > 0) {
				ListBulletSpan previousListItemSpan = aheadListItemSpans[aheadListItemSpans.length - 1];
				if (null != previousListItemSpan) {
					int pStart = editable
							.getSpanStart(previousListItemSpan);
					int pEnd = editable
							.getSpanEnd(previousListItemSpan);
					//
					// Handle this case:
					// 1. A
					// B
					// C
					// 1. D
					//
					// User puts focus to B and click List icon, to
					// change it to:
					// 2. B
					//
					// Then user puts focus to C and click List icon, to
					// change it to:
					// 3. C
					// For this one, we need to finish the span "2. B"
					// correctly
					// Which means we need to set the span end to a
					// correct value
					// This is doing this.
					if(pEnd == 0){
						editable.removeSpan(previousListItemSpan);
					}else if (editable.charAt(pEnd - 1) == Constants.CHAR_NEW_LINE) {
						editable.removeSpan(previousListItemSpan);
						editable.setSpan(previousListItemSpan, pStart,
								pEnd - 1,
								Spannable.SPAN_INCLUSIVE_INCLUSIVE);
					}

					makeLineAsBullet();
				}
			} else {
				//
				// Case 1
				makeLineAsBullet();
			}
		} else {
			//
			// Current line is list item span
			// By clicking the image view, we should remove the
			// BulletListItemSpan
			//

			int spanEnd = editable.getSpanEnd(listBulletSpans[0]);
			int spanStart = editable.getSpanStart(listBulletSpans[0]);
			editable.removeSpan(listBulletSpans[0]);

			if(editable.charAt(spanStart) == Constants.ZERO_WIDTH_SPACE_INT)
				editable.delete(spanStart, spanStart + 1);

			updateCheckStatus(false);
		}
		triggerTextChange();
		Logger.d("yjh", "ARE_ListBullet over onClick, spanSize=" + mEditText.getEditableText().getSpans(0,mEditText.getEditableText().length(),Object.class).length);
	}

	public void triggerTextChange(){
		int selectionStart = mEditText.getSelectionStart();
		int selectionEnd = mEditText.getSelectionEnd();

		Editable editable = mEditText.getText();
		Logger.d("triggerTextChange, select start=" + selectionStart + " end=" + selectionEnd);
		editable.insert(selectionEnd, Constants.ZERO_WIDTH_SPACE_STR);
		mEditText.startStorageMonitor();
		editable.delete(selectionEnd, min(editable.length(), selectionEnd + 1));
		mEditText.startApplyMonitor();
		mEditText.startUiShowMonitor();

		RichTextKTUtilsKt.recordApplyStyleToUndoRedo(selectionStart, selectionEnd, getIsChecked(), mEditText, ARE_ListBullet.SPAN_CLASS);
	}

	@Override
	public void applyStyle(Editable editable, int start, int end, Boolean isRecordToHistory) {
		logAllBulletListItems(editable);
		ListBulletSpan[] listSpans = editable.getSpans(start, end,
				ListBulletSpan.class);
		if (null == listSpans || listSpans.length == 0) {
			return;
		}

		Logger.d("listBulletSpan applyStyle start/end=" + start + "/" + end + " length=" + listSpans.length);

		if (end > start) {
			//
			// User inputs
			//
			// To handle the \n case

			// int totalLen = editable.toString().length();
			// Util.log("ListNumber - total len == " + totalLen);
			char c = editable.charAt(end - 1);
			if (c == Constants.CHAR_NEW_LINE) {
				int listSpanSize = listSpans.length;
				int previousListSpanIndex = listSpanSize - 1;
				if (previousListSpanIndex > -1) {
					ListBulletSpan previousListSpan = listSpans[previousListSpanIndex];
					int lastListItemSpanStartPos = editable
							.getSpanStart(previousListSpan);
					int lastListItemSpanEndPos = editable
							.getSpanEnd(previousListSpan);
					CharSequence listItemSpanContent = editable.subSequence(
							lastListItemSpanStartPos, lastListItemSpanEndPos);

					if (isEmptyListItemSpan(listItemSpanContent)) {
						//
						// Handle this case:
						// 1. A
						// 2. <User types \n here, at an empty span>
						//
						// The 2 chars are:
						// 1. ZERO_WIDTH_SPACE_STR
						// 2. \n
						//
						// We need to remove current span and do not re-create
						// span.
						editable.removeSpan(previousListSpan);

						//
						// Deletes the ZERO_WIDTH_SPACE_STR and \n
						editable.delete(lastListItemSpanStartPos,
								lastListItemSpanEndPos);
						updateCheckStatus(false);
						mEditText.post(new Runnable() {
							@Override
							public void run() {
								int selStart = mEditText.getSelectionStart();
								int selEnd = mEditText.getSelectionEnd();
								mEditText.onSelectionChanged(selStart, selEnd);
							}
						});
						return;
					} else {
						//
						// Handle this case:
						//
						// 1. A
						// 2. C
						// 3. D
						//
						// User types \n after 'A'
						// Then
						// We should see:
						// 1. A
						// 2.
						// 3. C
						// 4. D
						//
						// We need to end the first span
						// Then start the 2nd span
						// Then reNumber the following list item spans
						if (end > lastListItemSpanStartPos) {
							editable.removeSpan(previousListSpan);
							editable.setSpan(previousListSpan,
									lastListItemSpanStartPos, end - 1,
									Spanned.SPAN_INCLUSIVE_INCLUSIVE);
						}
					}
					makeLineAsBullet();
				} // #End of if it is in ListItemSpans..
			} // #End of user types \n
		} else {
			//
			// User deletes
			ListBulletSpan theFirstSpan = listSpans[0];
			if (listSpans.length > 0) {
				FindFirstAndLastBulletSpan findFirstAndLastBulletSpan = new FindFirstAndLastBulletSpan(editable, listSpans).invoke();
				theFirstSpan = findFirstAndLastBulletSpan.getFirstTargetSpan();
			}
			int spanStart = editable.getSpanStart(theFirstSpan);
			int spanEnd = editable.getSpanEnd(theFirstSpan);

			Util.log("Delete spanStart = " + spanStart + ", spanEnd = " + spanEnd);

			if (spanStart >= spanEnd) {
				Util.log("case 1");
				//
				// User deletes the last char of the span
				// So we think he wants to remove the span
				for (ListBulletSpan listSpan : listSpans) {
					editable.removeSpan(listSpan);
				}

				//
				// To delete the previous span's \n
				// So the focus will go to the end of previous span
				if (spanStart > 0) {
					editable.delete(spanStart - 1, spanEnd);
				}
			} else if (start == spanStart) {
                //*|XXXX
                for (Object what : listSpans) {
                    editable.removeSpan(what);
                }
                updateCheckStatus(false);

                // 检查当前行删除前缀后是否还有内容
                int currentLine = Util.getCurrentCursorLine(mEditText);
                int lineStart = Util.getThisLineStart(mEditText, currentLine);
                int lineEnd = Util.getThisLineEnd(mEditText, currentLine);

                // 检查行内容（排除零宽字符）
                boolean hasRealContent = false;
                if (lineEnd > lineStart) {
                    for (int i = lineStart; i < lineEnd; i++) {
                        if (i < editable.length() && editable.charAt(i) != Constants.ZERO_WIDTH_SPACE_INT && editable.charAt(i) != '\n') {
                            hasRealContent = true;
                            break;
                        }
                    }
                }

                if (hasRealContent) {
                    // 如果当前行还有内容，需要将内容合并到上一行并扩展上一行的span范围

                    // 找到上一行的ListBulletSpan
                    ListBulletSpan previousSpan = null;
                    if (start > 1) {
                        ListBulletSpan[] previousSpans = editable.getSpans(start - 2, start - 1, ListBulletSpan.class);
                        if (previousSpans != null && previousSpans.length > 0) {
                            previousSpan = previousSpans[previousSpans.length - 1];
                        }
                    }

                    // 删除零宽字符
                    if (start > 0 && start < editable.length() && editable.charAt(start) == Constants.ZERO_WIDTH_SPACE_INT) {
                        editable.delete(start, start + 1);
                        start--; // 调整位置
                    }

                    // 删除前面的换行符，让内容合并到上一行
                    if (start > 0 && editable.charAt(start - 1) == '\n') {
                        editable.delete(start - 1, start);
                        start--; // 调整位置
                    }

                    // 如果找到了上一行的span，扩展其范围包含合并过来的内容
                    if (previousSpan != null) {
                        int previousSpanStart = editable.getSpanStart(previousSpan);
                        int previousSpanEnd = editable.getSpanEnd(previousSpan);

                        // 检查span是否仍然有效（防止span已被其他地方删除导致getSpanStart返回-1）
                        if (previousSpanStart >= 0 && previousSpanEnd >= 0 && previousSpanStart <= previousSpanEnd) {
                            // 计算新的span结束位置（包含合并过来的内容）
                            int newLineEnd = Util.getThisLineEnd(mEditText, Util.getCurrentCursorLine(mEditText));
                            if (newLineEnd > 0 && newLineEnd <= editable.length() && editable.charAt(newLineEnd - 1) == '\n') {
                                newLineEnd--;
                            }

                            // 确保新的结束位置有效且不小于开始位置
                            if (newLineEnd >= previousSpanStart && newLineEnd <= editable.length()) {
                                // 重新设置span范围
                                editable.removeSpan(previousSpan);
                                editable.setSpan(previousSpan, previousSpanStart, newLineEnd, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                            } else {
                                // 如果新的结束位置无效，只移除span不重新设置
                                editable.removeSpan(previousSpan);
                            }
                        }
                        // 如果span已经无效，不做任何操作（span可能已被其他地方删除）
                    }
                } else {
                    // 如果当前行没有实际内容，按原逻辑处理
                    if (Util.getCurrentCursorLine(mEditText) != 0) {
                        editable.insert(start, "\n");
                    }
                }
                return;
			} else if (start == spanEnd) {
				Util.log("case 3");
				//
				// User deletes the first char of the span
				// So we think he wants to remove the span
				if (editable.length() > start) {
					if (editable.charAt(start) == Constants.CHAR_NEW_LINE) {
						// The error case to handle
						Util.log("case 3-1");
						ListBulletSpan[] spans = editable.getSpans(start, start, ListBulletSpan.class);
						Util.log(" spans len == " + spans.length);
						if (spans.length > 0) {
							mergeForward(editable, theFirstSpan, spanStart, spanEnd);
						}
					} else {
						mergeForward(editable, theFirstSpan, spanStart, spanEnd);
					}
				}
			} else if (start > spanStart && end < spanEnd) {
				//
				// Handle this case:
				// *. AAA1
				// *. BBB2
				// *. CCC3
				//
				// User deletes '1' / '2' / '3'
				// Or any other character inside of a span
				//
				// For this case we won't need do anything
				// As we need to keep the span styles as they are
				return;
			}
		}

		logAllBulletListItems(editable);
	} // # End of applyStyle(..)

	@Override
	public void removeStyle(Editable editable, int start, int end) {
		ListBulletSpan[] listBulletSpans = editable.getSpans(start,
				end, ListBulletSpan.class);
		if (null == listBulletSpans) {
			return;
		}
		for(ListBulletSpan span: listBulletSpans) {
			editable.removeSpan(span);
		}
	}

	@Override
	public void setStyleStatusListener(StyleStatusListener listener) {

	}

	@Override
	public Boolean needApplyStyle() {
		return true;
	}

	protected void mergeForward(Editable editable, ListBulletSpan listSpan, int spanStart, int spanEnd) {
		Logger.d("bullet","merge forward 1");
		if (editable.length() < spanEnd + 1) {
			return;
		}
		Logger.d("bullet","merge forward 2");
		ListBulletSpan[] targetSpans = editable.getSpans(
				spanEnd, spanEnd + 1, ListBulletSpan.class);
		if (targetSpans == null || targetSpans.length == 0) {
			return;
		}

		for(int s=spanEnd; s<editable.length(); s++){
//            Logger.d("for s=" + s + " c=" + editable.charAt(s));
			if(editable.charAt(s) == Constants.ZERO_WIDTH_SPACE_INT) {
				mEditText.stopAllMonitor();
				editable.delete(s, s + 1);
				mEditText.startAllMonitor();
				targetSpans = editable.getSpans(
						spanEnd, spanEnd + 1, ListBulletSpan.class);
				if (targetSpans == null || targetSpans.length == 0) {
					return;
				}
			}
		}

		FindFirstAndLastBulletSpan findFirstAndLastBulletSpan = new FindFirstAndLastBulletSpan(editable, targetSpans).invoke();
		ListBulletSpan firstTargetSpan = findFirstAndLastBulletSpan.getFirstTargetSpan();
		ListBulletSpan lastTargetSpan = findFirstAndLastBulletSpan.getLastTargetSpan();
		int targetStart = editable.getSpanStart(firstTargetSpan);
		int targetEnd = editable.getSpanEnd(lastTargetSpan);

		// 检查目标span是否仍然有效
		if (targetStart < 0 || targetEnd < 0 || targetStart > targetEnd) {
			Logger.d("bullet","merge forward failed: target spans are invalid");
			return;
		}

		Logger.d("bullet","merge to remove span start == " + targetStart + ", target end = " + targetEnd);

		int targetLength = targetEnd - targetStart;
		spanEnd = spanEnd + targetLength;

		// 检查最终的span范围是否有效
		if (spanStart < 0 || spanEnd < 0 || spanStart > spanEnd || spanEnd > editable.length()) {
			Logger.d("bullet","merge forward failed: final span range is invalid");
			return;
		}

		for (ListBulletSpan targetSpan : targetSpans) {
			editable.removeSpan(targetSpan);
		}
		ListBulletSpan[] compositeSpans = editable.getSpans(spanStart, spanEnd, ListBulletSpan.class);
		for (ListBulletSpan lns : compositeSpans) {
			editable.removeSpan(lns);
		}
		editable.setSpan(listSpan, spanStart, spanEnd, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
		Logger.d("bullet","merge span start == " + spanStart + " end == " + spanEnd);
	}

	private void logAllBulletListItems(Editable editable) {
		ListBulletSpan[] listItemSpans = editable.getSpans(0,
				editable.length(), ListBulletSpan.class);
		for (ListBulletSpan span : listItemSpans) {
			int ss = editable.getSpanStart(span);
			int se = editable.getSpanEnd(span);
			Util.log("List All: " + " :: start == " + ss + ", end == " + se);
		}
	}

	/**
	 * Check if this is an empty span.
	 * 
	 * <B>OLD COMMENT: and whether it is at the end of the spans list</B>
	 * 
	 * @param listItemSpanContent
	 * @return
	 */
	private boolean isEmptyListItemSpan(CharSequence listItemSpanContent) {
		int spanLen = listItemSpanContent.length();
        if (spanLen == 1 && listItemSpanContent.charAt(0) == Constants.CHAR_NEW_LINE) {
            return true;
        } else if (spanLen == 2
                && listItemSpanContent.charAt(0) == Constants.ZERO_WIDTH_SPACE_INT
                && listItemSpanContent.charAt(1) == Constants.CHAR_NEW_LINE) {
            //
            // This case:
            // 1. A
            // 2.
            //
            // Line 2 is empty
            return true;
        } else {
			return false;
		}
	}

	/**
	 * 
	 * @return
	 */
	private ListBulletSpan makeLineAsBullet() {
		Logger.d("makeLineAsList");
		int currentLine = Util.getCurrentCursorLine(mEditText);
		int start = Util.getThisLineStart(mEditText, currentLine);
		int end = Util.getThisLineEnd(mEditText, currentLine);
		Editable editable = mEditText.getText();

		boolean wasEmptyLine = !(end > start);

		// 分析行内容
		StringBuilder lineContent = new StringBuilder();
		for (int i = start; i < end && i < editable.length(); i++) {
			char c = editable.charAt(i);
			if (c == Constants.ZERO_WIDTH_SPACE_INT) {
				lineContent.append("[ZWS]");
			} else if (c == '\n') {
				lineContent.append("\\n");
			} else {
				lineContent.append(c);
			}
		}

		Logger.d("ARE_ListBullet", "makeLineAsBullet: line=" + currentLine + ", start=" + start + ", end=" + end + ", wasEmptyLine=" + wasEmptyLine + ", lineContent='" + lineContent.toString() + "'");

		if(wasEmptyLine) {
			editable.insert(start, Constants.ZERO_WIDTH_SPACE_STR);
			Logger.d("ARE_ListBullet", "makeLineAsBullet: inserted zero-width char at position " + start);
		} else {
			// 检查行首是否已有零宽字符，如果没有则插入（参考待办事项的逻辑）
			if (start < editable.length() && editable.charAt(start) != Constants.ZERO_WIDTH_SPACE_INT) {
				editable.insert(start, Constants.ZERO_WIDTH_SPACE_STR);
				Logger.d("ARE_ListBullet", "makeLineAsBullet: inserted zero-width char at position " + start + " for non-empty line");
			} else {
				Logger.d("ARE_ListBullet", "makeLineAsBullet: zero-width char already exists at position " + start);
			}
		}

		start = Util.getThisLineStart(mEditText, currentLine);
		end = Util.getThisLineEnd(mEditText, currentLine);

		if (end > 0 && editable.charAt(end - 1) == Constants.CHAR_NEW_LINE) {
			end--;
		}
		ListBulletSpan BulletListItemSpan = new ListBulletSpan();
		editable.setSpan(BulletListItemSpan, start, end,
				Spannable.SPAN_INCLUSIVE_INCLUSIVE);

		// 修正光标位置：无论是空行还是非空行，只要插入了零宽字符就需要修正
		mEditText.post(new Runnable() {
			@Override
			public void run() {
				int currentCursor = mEditText.getSelectionStart();
				int lineStart = Util.getThisLineStart(mEditText, Util.getCurrentCursorLine(mEditText));
				// 如果光标在行首的零宽字符位置，移动到零宽字符之后
				if (currentCursor == lineStart && lineStart < editable.length() &&
					editable.charAt(lineStart) == Constants.ZERO_WIDTH_SPACE_INT) {
					mEditText.setSelection(lineStart + 1);
					Logger.d("ARE_ListBullet", "makeLineAsBullet: cursor moved from " + currentCursor + " to " + (lineStart + 1));
				} else {
					Logger.d("ARE_ListBullet", "makeLineAsBullet: no cursor fix needed, cursor=" + currentCursor + ", lineStart=" + lineStart);
				}
			}
		});

		return BulletListItemSpan;
	}

	/**
	 * Change the selected {@link ListNumberSpan} to {@link ListBulletSpan}
	 * 
	 * @param listNumberSpans
	 */
	private void changeListNumberSpanToListBulletSpan(
			Editable editable,
			ListNumberSpan[] listNumberSpans) {

		if (null == listNumberSpans || listNumberSpans.length == 0) {
			return;
		}

		// - 
		// Handle this case:
		// User has:
		// 
		// 1. AA
		// 2. BB
		// 3. CC
		// 4. DD
		//
		// Then user clicks Bullet icon at line 2:
		//
		// So it should change to:
		// 1. AA
		// * BB
		// 1. CC
		// 2. DD 
		// 
		// So this is for handling the line after 2nd line.
		// "CC" starts from 1 again.
		// 
		// - Restart the count after the bullet span
		int len = listNumberSpans.length;
		ListNumberSpan lastListNumberSpan = listNumberSpans[len - 1];
		int lastListNumberSpanEnd = editable.getSpanEnd(lastListNumberSpan);
		
		ARE_ListNumber.reNumberBehindListItemSpans(lastListNumberSpanEnd + 1, editable, 0);
		
		// 
		// - Replace all ListNumberSpan to ListBulletSpan
		for (ListNumberSpan listNumberSpan : listNumberSpans) {
			int start = editable.getSpanStart(listNumberSpan);
			int end = editable.getSpanEnd(listNumberSpan);
			
			editable.removeSpan(listNumberSpan);
			ListBulletSpan listBulletSpan = new ListBulletSpan();
			editable.setSpan(listBulletSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
		}
		
	} // #End of changeListNumberSpansToListBulletSpans(..)

	private void changeListUpcomingToBullet(Editable editable,
											UpcomingListSpan[] upcomingListSpans) {
		if (null == upcomingListSpans || upcomingListSpans.length == 0) {
			return;
		}
		// - Restart the count after the bullet span
		int len = upcomingListSpans.length;
		UpcomingListSpan upcomingListSpan = upcomingListSpans[len - 1];
		int lastListUpcomingSpanEnd = editable.getSpanEnd(upcomingListSpan);

		// -- Change the content to trigger the editable redraw
		//editable.insert(lastListUpcomingSpanEnd, Constants.ZERO_WIDTH_SPACE_STR);
		//editable.delete(lastListUpcomingSpanEnd + 1, lastListUpcomingSpanEnd + 1);
		// -- End: Change the content to trigger the editable redraw

		// - Replace all ListUpcomingSpan to ListBulletSpan
		for (UpcomingListSpan upcomingSpan : upcomingListSpans) {
			int start = editable.getSpanStart(upcomingSpan);
			int end = editable.getSpanEnd(upcomingSpan);

			StrikethroughSpan[] strikethroughSpans = mEditText.getText().getSpans(start,end,StrikethroughSpan.class);
			ForegroundColorSpan
					[] foregroundColorSpans = mEditText.getText().getSpans(start,end, ForegroundColorSpan.class);

			if (strikethroughSpans.length > 0) {
				mEditText.getText().removeSpan(strikethroughSpans[0]);
			}
			if (foregroundColorSpans.length > 0) {
				mEditText.getText().removeSpan(foregroundColorSpans[0]);
			}

			editable.removeSpan(upcomingSpan);
			ListBulletSpan listBulletSpan = new ListBulletSpan();
			editable.setSpan(listBulletSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
		}
	}


	private class FindFirstAndLastBulletSpan {
		private Editable editable;
		private ListBulletSpan[] targetSpans;
		private ListBulletSpan firstTargetSpan;
		private ListBulletSpan lastTargetSpan;

		public FindFirstAndLastBulletSpan(Editable editable, ListBulletSpan... targetSpans) {
			this.editable = editable;
			this.targetSpans = targetSpans;
		}

		public ListBulletSpan getFirstTargetSpan() {
			return firstTargetSpan;
		}

		public ListBulletSpan getLastTargetSpan() {
			return lastTargetSpan;
		}

		public FindFirstAndLastBulletSpan invoke() {
			firstTargetSpan = targetSpans[0];
			lastTargetSpan = targetSpans[0];
			if (targetSpans.length > 0) {
                int firstTargetSpanStart = editable.getSpanStart(firstTargetSpan);
                int lastTargetSpanEnd = editable.getSpanEnd(firstTargetSpan);
                for (ListBulletSpan lns : targetSpans) {
                    int lnsStart = editable.getSpanStart(lns);
                    int lnsEnd = editable.getSpanEnd(lns);
                    if (lnsStart < firstTargetSpanStart) {
                        firstTargetSpan = lns;
                        firstTargetSpanStart = lnsStart;
                    }
                    if (lnsEnd > lastTargetSpanEnd) {
                        lastTargetSpan = lns;
                        lastTargetSpanEnd = lnsEnd;
                    }
                }
            }
			return this;
		}
	}
}
