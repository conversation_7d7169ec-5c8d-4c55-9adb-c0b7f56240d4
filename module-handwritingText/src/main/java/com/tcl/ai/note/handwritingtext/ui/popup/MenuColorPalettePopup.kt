package com.tcl.ai.note.handwritingtext.ui.popup

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.bean.PenColor
import com.tcl.ai.note.handwritingtext.ui.swatches.TabletColorPalette
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.toPx


/**
 * 色盘popup
 */
@Composable
fun MenuColorPalettePopup(
    isDarkTheme:Boolean = isSystemInDarkTheme(),
    menuBarItem: MenuBarItem,
    collectColors:List<Color>,
    curPenColor: PenColor,
    onConfirm:(penColor:PenColor) ->Unit,
    onDismissRequest: () -> Unit) {

    val space = 23.dp
    val dimens = getGlobalDimens()
    val popupWidth = dimens.colorPaletteWidth
    val density = LocalDensity.current
    val layoutDirection = LocalLayoutDirection.current
    val screenWidthDp = LocalConfiguration.current.screenWidthDp
    val position: Offset = menuBarItem.position
    val xOffset = with(density) {
        if (layoutDirection == LayoutDirection.Rtl) {
            (screenWidthDp.dp.toPx() - position.x - dimens.btnSize.toPx / 2 - popupWidth.toPx() / 2).toInt()
        } else {
            (position.x + dimens.btnSize.toPx / 2 - popupWidth.toPx() / 2).toInt()
        }

    }

    val yOffset =(space.toPx+position.y).toInt()
    FocusedBounceScalePopup(
        onDismissRequest = onDismissRequest,
        offset = IntOffset(xOffset, yOffset),
    ){ closePopup ->
        Box(
            modifier = Modifier
                .width(popupWidth)
                .defShadow(20.dp)
        ) {
            TabletColorPalette(
                modifier = Modifier.fillMaxWidth(),
                onConfirm =onConfirm,
                collectColors =isDarkTheme.judge(
                    collectColors.map { it.inverseColor() },
                    collectColors
                ),
                curPenColor =isDarkTheme.judge(
                    curPenColor.copy(color = curPenColor.color.inverseColor()),
                    curPenColor
                ) ,
                onDismiss = {
                    closePopup()
                }
            )
        }

    }
}