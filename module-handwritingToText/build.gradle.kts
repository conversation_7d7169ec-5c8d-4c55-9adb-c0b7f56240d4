plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.jetbrains.kotlin.android)
    alias(libs.plugins.google.dagger.hilt)
    alias(libs.plugins.compose.compiler)
    alias(libs.plugins.google.devtools.ksp)
}
val standaloneHandwritingToText = project.findProperty("standaloneHandwritingToText")?.toString()?.toBoolean() ?: false
android {
    namespace = "com.tcl.ai.note.hwtt"
    compileSdk = libs.versions.compileSdk.get().toIntOrNull() ?: 35

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toIntOrNull() ?: 34

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }
    buildFeatures {
        buildConfig = true
    }
    flavorDimensions.add("device")
    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    productFlavors {
        create("phone") {
            dimension = "device"
            buildConfigField("boolean", "IS_PHONE", "true")
        }
        create("tablet") {
            dimension = "device"
            buildConfigField("boolean", "IS_PHONE", "false")
        }
    }

    sourceSets {
        getByName("phone") {
            java.srcDirs("src/phone/java")
            res.srcDirs("src/phone/res")
            manifest.srcFile("src/phone/AndroidManifest.xml")
        }
        getByName("tablet") {
            java.srcDirs("src/tablet/java")
            res.srcDirs("src/tablet/res")
            manifest.srcFile("src/tablet/AndroidManifest.xml")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
    buildFeatures {
        compose = true
    }

}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    //implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    //implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.compose.material3)
    implementation(libs.androidx.adaptive)
    implementation(libs.androidx.lifecycle.service)
    implementation(libs.tcl.componentfrm)
    implementation(libs.androidx.fragment)
    implementation(libs.androidx.appcompat)
    implementation(libs.dagger.hilt)
    implementation(libs.androidx.constraintlayout.compose.android)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.ui.android)
    implementation(libs.androidx.foundation.android)
    ksp(libs.dagger.hilt.compiler)
    implementation(libs.androidx.lifecycle.viewmodel.compose)
    implementation(libs.androidx.navigation.compose)
    implementation(libs.androidx.hilt.navigation.compose)
    implementation(libs.okhttp3.okhttp)
    implementation(libs.retrofit)
    implementation(libs.airbnb.lottie.compose)
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    implementation(libs.coil.compose)
    implementation(libs.coil.network.okhttp)
    implementation(libs.compose.ui)
    implementation(libs.compose.ui.tooling)
    implementation(libs.compose.foundation)
    implementation (libs.digital.ink.recognition)
    implementation(libs.kotlinx.coroutines.play.services)
    implementation(libs.tclui.compose)
    implementation(libs.tclui.compose.icons)
    lintChecks(libs.tclui.compose.lint)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)

    implementation(project(":module-base"))
    implementation(project(":module-handwritingText"))
}