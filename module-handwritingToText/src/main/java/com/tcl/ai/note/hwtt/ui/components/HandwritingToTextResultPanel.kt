package com.tcl.ai.note.hwtt.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.widget.verticalScrollbar
import com.tcl.ai.note.base.R
import com.tcl.ai.note.hwtt.states.DownloadLanguageConfigState
import com.tcl.ai.note.hwtt.states.DownloadStatus
import com.tcl.ai.note.hwtt.states.HandwritingToTextMsg
import com.tcl.ai.note.hwtt.states.StreamingStatus
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.widget.components.AICommonResultPanelColumn
import com.tcl.ai.note.widget.components.AIBottomOperateComponent
import com.tcl.ai.note.widget.components.AIBottomOperateComponentForHandwriting
import com.tcl.ai.note.widget.components.AIBottomPrompt
import com.tcl.ai.note.widget.components.AIStateLoading
import com.tcl.ai.note.widget.components.AIStateLoadingRetry
import com.tcl.ai.note.widget.components.BottomFadeBox

@Composable
internal fun HandwritingToTextResultPanel(
    downloadState: DownloadLanguageConfigState,
    msg: HandwritingToTextMsg,
    onRetryClick: () -> Unit,
    onReplaceClick: (String) -> Unit,
    onCopyClick: () -> Unit,
    onStopClick: (Boolean) -> Unit,
    onOutput: (String) -> Unit,
    modifier: Modifier,
    showToast: (toastId: Int) -> Unit = {},
    isOffline: Boolean
) {
    val density = LocalDensity.current

    AICommonResultPanelColumn(modifier = modifier) {
        when (downloadState.status) {
            DownloadStatus.DOWNLOADING, DownloadStatus.LOADING -> {
                val content=if (downloadState.status == DownloadStatus.DOWNLOADING){
                    stringResource(id = R.string.downloading_tip)
                }else{
                    ""
                }
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    AIStateLoading(isOffline = isOffline, showStopButton =downloadState.status != DownloadStatus.DOWNLOADING , tip = content, onStopClick = {onStopClick(it)})
                }
            }

            DownloadStatus.SUCCESS -> {

                val scrollState = rememberScrollState()
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                ) {
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxWidth()
                    ) {
                        LaunchedEffect(key1 = downloadState) {
                            scrollState.animateScrollTo(scrollState.maxValue)
                        }
                        val isScrollEnd = remember {
                            derivedStateOf {
                                scrollState.value == scrollState.maxValue
                            }
                        }
                        BottomFadeBox(
                            modifier = Modifier.fillMaxWidth(),
                            isDrawGradient = !isScrollEnd.value
                        ) {
                            Text(
                                text = msg.recognitionResult,//加两个换行符，让最后一行文字不被遮挡
                                fontSize = if (isTablet) 14.sp else 16.sp,
                                modifier = Modifier
                                    .fillMaxSize()
                                    .verticalScrollbar(
                                        state = scrollState,
                                        alwaysShowScrollBar = true,
                                        scrollbarHeightOffsetY = with(density) { (13.dp).toPx() })
                                    .verticalScroll(scrollState)
                                    .padding(bottom = 14.dp, end = 10.dp),
                                color = colorResource(id = R.color.text_title)
                            )
                        }
                        if (msg.status == StreamingStatus.IN_PROGRESS) {
                            AIStateLoading(
                                isOffline = isOffline,
                                false,
                                onStopClick = { onStopClick(it) })
                        }

                    }
                    AIBottomOperateComponentForHandwriting(
                        content = msg.recognitionResult,
                        modifier = Modifier.fillMaxWidth(),
                        onRetryClick = null,
                        onCopyClick = {
                            onCopyClick()
                            showToast(R.string.copied)
                        },
                        onReplaceClick = {
                            onReplaceClick(it)
                            showToast(R.string.replaced)
                        }
                    )
                }

            }

            DownloadStatus.ERROR, DownloadStatus.STOP -> {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxSize(),
                    contentAlignment = Alignment.Center // 设置内容对齐方式为居中
                ) {
                    val content = if (downloadState.status == DownloadStatus.ERROR) {
                       if (downloadState.errorRes!=null){
                           stringResource(downloadState.errorRes)
                       }else{
                           stringResource(R.string.network_error)
                       }
                    } else {
                        ""
                    }
                    Text(text = content,
                            fontSize = 16.sp,
                            modifier = Modifier.fillMaxSize()
                        )
                    AIStateLoadingRetry(onBtnClick = {onRetryClick()})

                    AIBottomOperateComponent(
                        content = msg.recognitionResult,
                        modifier = Modifier.align(Alignment.BottomCenter),
                        onRetryClick = null,
                        onCopyClick = {
                            onCopyClick()
                            showToast(R.string.copied)
                        },
                        onInsertClick ={
                            onReplaceClick(it)
                            showToast(R.string.inserted)
                        }
                    )
                }
            }

            else -> {
                AIBottomOperateComponent(
                    content = msg.recognitionResult,
                    modifier = Modifier.weight(1f),
                    onRetryClick = null,
                    onCopyClick = {
                        onCopyClick()
                        showToast(R.string.copied)
                    },
                    onInsertClick ={
                        onReplaceClick(it)
                        showToast(R.string.inserted)
                    }
                )
            }
        }
        if (!isTablet) {
            AIBottomPrompt()
        }
    }
}


@Preview(showBackground = true)
@Composable
private fun HandwritingToTextResultPanelPreview() {
    // 模拟的参数
    val mockDownloadStatus = DownloadLanguageConfigState(status = DownloadStatus.SUCCESS)
    val mockMsg = HandwritingToTextMsg()
    val mockOnRetryClick = { /* 模拟重试点击逻辑 */ }
    val mockOnReplaceClick = {str:String->
    /* 模拟复制点击逻辑 */
    }
    val mockOnCopyClick = { /* 模拟复制点击逻辑 */ }
    val mockOnOutput = { output: String -> /* 模拟输出逻辑 */ }
    val mockModifier = Modifier

    // 调用组件
    HandwritingToTextResultPanel(
        downloadState = mockDownloadStatus,
        msg = mockMsg,
        onRetryClick = mockOnRetryClick,
        onReplaceClick = mockOnReplaceClick,
        onCopyClick = mockOnCopyClick,
        onStopClick = {},
        onOutput = mockOnOutput,
        modifier = mockModifier,
        {},
        false

    )
}