package com.tcl.ai.note.home.components.notelist.content

import androidx.compose.foundation.Image
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.TransformOrigin
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.gif.GifDecoder
import coil3.imageLoader
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.components.base.rememberNoteLayoutConfig
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.home.model.ThumbnailType
import com.tcl.ai.note.home.utils.rememberScaledCardSize
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getScreenSize
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.isTabletLandscape
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.widget.components.BottomFadeBox

@Composable
internal fun CardTypeContent(note: HomeNoteItemModel) {
    Logger.d("HomeNoteItemCard", "thumbnailType:${note.thumbnailType}")
    val noteDes = note.summary ?: ""
    when (note.thumbnailType) {
        ThumbnailType.FIRST_SCREEN -> {
            // 显示第一屏信息（图片/手绘）
            FirstScreenImageDisplay(note)
        }

        ThumbnailType.PURE_TEXT -> {
            // 显示富文本内容
            BottomFadeBox(
                modifier = Modifier
                    .fillMaxSize()
                    .semantics {
                        contentDescription = noteDes
                    }
                    .padding(12.dp),
                fadeHeight = 24.dp
            ) {
                HomeRichTextPreview(
                    note = note,
                    modifier = Modifier.fillMaxSize(),
                    isNormalMode = true
                )
            }
        }

        ThumbnailType.AUDIO_ICON -> {
            // 显示录音图标
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_note_only_audio),
                    contentDescription = stringResource(R.string.audio_title),
                    colorFilter = ColorFilter.tint(colorResource(R.color.home_note_list_icon_hint_color)),
                    modifier = Modifier.size(100.dp)
                )
            }
        }

        ThumbnailType.TEXT_ICON -> {
            OnlyTextIcon()
        }

        ThumbnailType.MIXED_CONTENT -> {
            MixContent(note)
        }

        ThumbnailType.UNKNOWN -> {
            //未知类型
        }
    }
}

/**
 * 混合模式：底部富文本，上层叠加手绘
 */
@Composable
private fun MixContent(note: HomeNoteItemModel) {
    val containerSize = rememberScaledCardSize()
    val handwritingThumbnail = getHandwritingThumbnail(note)
    val imageCacheKey = "$handwritingThumbnail${note.modifyTime}"
    val contentDescription = stringResource(R.string.handwritten_title)
    val isImageType = note.isImageType

    Box {
        HomeThumbnailPreview(
            targetWidth = containerSize.width.dp,
            targetHeight = containerSize.height.dp
        ) {
            HomeRichTextPreview(
                note = note,
                modifier = Modifier
                    .fillMaxSize(),
                isNormalMode = false
            )
            HomeAsyncImage(
                modifier = Modifier.fillMaxSize(),
                data = handwritingThumbnail,
                imageCacheKey = imageCacheKey,
                contentScale = if (isImageType) ContentScale.Fit else ContentScale.Fit,
                contentDescription = null
            )
        }
        //主要用于无障碍talkback 的蒙版 因为上面的缩略图是宽高全屏缩放而来的，焦点有问题
        Box(
            modifier = Modifier
                .size(containerSize.width.dp, containerSize.height.dp)
                .align(Alignment.Center)
                .semantics() {
                    contentDescription.let { this.contentDescription = it }
                })
    }
}

/**
 * 根据夜间模式选择合适的缩略图
 */
@Composable
private fun getHandwritingThumbnail(note: HomeNoteItemModel): String? {
    val handwritingThumbnail = if (isSystemInDarkTheme()) {
        note.handwritingThumbnailDark ?: note.handwritingThumbnail
    } else {
        note.handwritingThumbnail
    }
    return handwritingThumbnail
}

/**
 *  显示文本图标
 */
@Composable
private fun OnlyTextIcon() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_note_only_text),
            contentDescription = stringResource(R.string.text_speaker),
            colorFilter = ColorFilter.tint(colorResource(R.color.home_note_list_icon_hint_color)),
            modifier = Modifier.size(100.dp)
        )
    }
}

/**
 *  显示第一屏图片/手绘
 */
@Composable
private fun FirstScreenImageDisplay(note: HomeNoteItemModel) {

    // 优先级：手绘缩略图 > 首图 > image字段
    val handwritingThumbnail = getHandwritingThumbnail(note)
    val isImageType = note.isImageType
    Logger.d(
        "HomeNoteItemCard",
        "AsyncImageWithFallback - handwritingThumbnail: $handwritingThumbnail,  isImageType: $isImageType  date: ${note.date}"
    )

    // 确定要显示的图片URI和缓存键
    val (imageUri, imageCacheKey) = when {
        !handwritingThumbnail.isNullOrEmpty() -> {
            Logger.d("HomeNoteItemCard", "Using handwritingThumbnail: $handwritingThumbnail")
            handwritingThumbnail to "$handwritingThumbnail${note.modifyTime}"
        }

        else -> {
            Logger.w("HomeNoteItemCard", "No valid image URI found, showing placeholder")
            null to ""
        }
    }
    val containerSize = rememberScaledCardSize()
    HomeThumbnailPreview(
        targetWidth = containerSize.width.dp,
        targetHeight = containerSize.height.dp,
    ) {
        HomeAsyncImage(
            modifier = Modifier
                .fillMaxSize(),
            data = imageUri,
            imageCacheKey = imageCacheKey,
            contentScale = if (isImageType) ContentScale.Fit else ContentScale.Fit,
            contentDescription = stringResource(R.string.handwritten_title)
        )
    }
}

/**
 * 异步加载图片组件
 * contentScale 默认为 FillBounds，能两边对齐,
 * ContentScale.Fit
 * ContentScale.Crop 更圆一点
 *
 */
@Composable
fun HomeAsyncImage(
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.FillBounds,
    data: Any?,
    imageCacheKey: String,
    contentDescription: String? = stringResource(R.string.image_title)
) {

    if (data != null) {
        val context = LocalContext.current
        Logger.d("HomeAsyncImage", "Loading image with URI: $data, cacheKey: $imageCacheKey")
        // 使用全局的ImageLoader，避免在每个列表项中重复创建
        val imageLoader = LocalContext.current.imageLoader.newBuilder().components {
            GifDecoder.Factory()
        }.build()
        val imageRequest = ImageRequest.Builder(context)
            .data(data)
            .crossfade(true)
            .memoryCacheKey(imageCacheKey)
            .diskCacheKey(imageCacheKey)
            .listener(
                onSuccess = { _, _ ->
                    Logger.d("HomeAsyncImage", "Image loaded successfully: $data")
                },
                onError = { _, error ->
                    Logger.e(
                        "HomeAsyncImage",
                        "Image loading failed for: $data, error: ${error.throwable.message}"
                    )
                }
            )
            .build()
        AsyncImage(
            model = imageRequest,
            imageLoader = imageLoader,
            contentDescription = contentDescription,
            contentScale = contentScale, // 填充父容器
            modifier = modifier,
        )
    } else {
        Logger.w("HomeAsyncImage", "Image URI is null, skipping image loading")
    }
}

@Composable
private fun MixedContentDisplay(note: HomeNoteItemModel) {
    val noteLayoutConfig = rememberNoteLayoutConfig()
    val cardSize = noteLayoutConfig.cardSize
    val cardWidth = cardSize.width.dp
    val cardHeight = cardSize.height.dp
    val screenSize = getScreenSize()
    val widthDp = screenSize.width().px2dp.dp
    val heightDp = screenSize.height().px2dp.dp
    var scaleXValue = cardWidth / widthDp
    var scaleYValue = cardHeight / heightDp
    if (isTabletLandscape) {
        scaleXValue = cardWidth / heightDp
        scaleYValue = cardHeight / widthDp
    }
    Logger.d("HomeNoteItemCard", "scaleXValue: $scaleXValue, scaleYValue: $scaleYValue")
    Logger.d("HomeNoteItemCard", "widthDp: $widthDp, heightDp: $heightDp")
    Logger.d("HomeNoteItemCard", "cardWidth: $cardWidth, cardHeight: $cardHeight")
    val textTransY = if (isTablet) {
        if (isTabletLandscape) -24.dp.toPx else -0.dp.toPx
    } else 10.dp.toPx
    val imageOffset = if (isTablet) {
        if (isTabletLandscape) IntOffset(
            y = (-28).dp.toPx.toInt(),
            x = (-5).dp.toPx.toInt()
        ) else IntOffset(y = (-42).dp.toPx.toInt(), x = (-5).dp.toPx.toInt())
    } else IntOffset(y = (-25).dp.toPx.toInt(), x = (-5).dp.toPx.toInt())
    Box(modifier = Modifier.fillMaxSize()) {
        if (isTabletLandscape) {
            HomeRichTextPreview(
                note = note,
                modifier = Modifier
                    .requiredSize(heightDp)
                    .graphicsLayer {
                        translationY = textTransY
                        transformOrigin = TransformOrigin(0.5f, 0.5f)
                        scaleX = scaleXValue
                        scaleY = scaleYValue
                    }
                    .align(Alignment.TopCenter),
                isNormalMode = false
                // 启用缩略图模式
            )
        } else {
            HomeRichTextPreview(
                note = note,
                modifier = Modifier
                    .requiredSize(widthDp, heightDp)
                    .graphicsLayer {
                        translationY = textTransY
                        transformOrigin = TransformOrigin(0.5f, 0.5f)
                        scaleX = scaleXValue
                        scaleY = scaleYValue
                    }
                    .align(Alignment.TopCenter),
                isNormalMode = false
                // 启用缩略图模式
            )
        }
        OptimizedMixedContentHandwritingDisplay(
            note = note,
            modifier = Modifier
                .requiredSize(widthDp)
                .graphicsLayer {
                    translationY = textTransY
                    transformOrigin = TransformOrigin(0.5f, 0.5f)
                    scaleX = scaleXValue
                    scaleY = scaleYValue
                }
        )
    }
}
