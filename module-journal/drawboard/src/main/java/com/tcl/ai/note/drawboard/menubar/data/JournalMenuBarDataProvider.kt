package com.tcl.ai.note.drawboard.menubar.data

import com.tcl.ai.note.base.R
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.drawboard.menubar.handler.JournalMenuBarEventHandler
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem
import com.tcl.ai.note.handwritingtext.bean.menHullIcon
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.data.MenuBarDependencies
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.provider.MenuBarDataProvider
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.provider.MenuBarUiState
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.drawboard.R as DBR

/**
 * 菜单栏数据提供者
 * 负责根据当前状态生成菜单项数据源
 *
 * 优化后使用依赖组合，减少参数数量
 */
class JournalMenuBarDataProvider(
    private val eventHandler: JournalMenuBarEventHandler,
    private val dependencies: MenuBarDependencies
): MenuBarDataProvider(eventHandler, dependencies) {

    /**
     * 获取绘图模式下的菜单项
     */
    fun getDrawMenuItems(
        menuBarState: MenuBarUiState,
        fingerDrawEnabled: Boolean,
    ): List<MenuBarItem> {
        val selectedPen = dependencies.penToolbarViewModel.selectedPen

        return if (DataStoreParam.IS_IFA) {
            listOf(
                // 笔刷
                MenuBarItem.Brush.apply {
                    onClick = eventHandler::handleBrushClick
                    isChecked =
                        menuBarState.currentMenuType == MenuBar.BRUSH && menuBarState.isBrushActive
                    iconRes = selectedPen.menHullIcon()
                },
                // 笔迹美化
                MenuBarItem.Beautify.apply {
                    onClick = { _ -> eventHandler.handleBeautifyClick() }
                },
                // 橡皮擦
                MenuBarItem.Eraser.apply {
                    isChecked = menuBarState.isEraserActive
                    onClick = eventHandler::handleEraserClick
                },
                // 关闭手指绘制
                MenuBarItem.PanOnly.apply {
                    onClick = { _ -> eventHandler.handleFingerSwitchClick() }
                    iconRes = fingerDrawEnabled.judge(
                        R.drawable.ic_finger_draw_enable,
                        R.drawable.ic_finger_draw_disable
                    )
                }
            )
        } else {
            listOf(
                // 键盘切换icon（切换到TEXT模式）
                MenuBarItem.Keyboard.apply {
                    isChecked = false
                    onClick = { _ -> eventHandler.handleKeyboardSwitchFromDraw() }
                    iconRes = DBR.drawable.ic_menu_exit_draw
                    descriptionRes = R.string.edit_top_menu_back_icon
                },
                // 笔刷
                MenuBarItem.Brush.apply {
                    onClick = eventHandler::handleBrushClick
                    isChecked =
                        menuBarState.currentMenuType == MenuBar.BRUSH && menuBarState.isBrushActive
                    iconRes = selectedPen.menHullIcon()
                },
                // 笔迹美化
                MenuBarItem.Beautify.apply {
                    onClick = { _ -> eventHandler.handleBeautifyClick() }
                },
                // 橡皮擦
                MenuBarItem.Eraser.apply {
                    isChecked = menuBarState.isEraserActive
                    onClick = eventHandler::handleEraserClick
                },
                // 关闭手指绘制
                MenuBarItem.PanOnly.apply {
                    onClick = { _ -> eventHandler.handleFingerSwitchClick() }
                    iconRes = fingerDrawEnabled.judge(
                        R.drawable.ic_finger_draw_enable,
                        R.drawable.ic_finger_draw_disable
                    )
                }
            )
        }
    }
}
