package com.tcl.ai.note.drawboard.menubar.handler

import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.data.MenuBarConfig
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.data.MenuBarDependencies
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.handler.MenuBarEventHandler

/**
 * 菜单栏事件处理器
 * 负责处理所有菜单栏相关的点击事件和业务逻辑
 *
 * 优化后使用依赖组合，减少参数数量
 */
class JournalMenuBarEventHandler(
    private val dependencies: MenuBarDependencies,
    private val config: MenuBarConfig
): MenuBarEventHandler(dependencies, config) {

    fun handleFingerSwitchClick() {
        // 处理手指切换点击事件
        dependencies.suniaDrawViewModel.enableFingerDrawing = !dependencies.suniaDrawViewModel.enableFingerDrawing
    }

    /**
     * 处理键盘切换事件（绘图模式到文本模式）
     */
    fun handleExitDrawMode() {
        with(dependencies) {
            menuBarViewModel.switchToTextEditMode()
            textAndDrawViewModel.editMode = EditMode.DRAW
        }
    }
}
